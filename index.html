<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Nashop: The Future of Decentralized Shopping | Earn $DAN Tokens</title>
    <meta name="description" content="Explore Nashop, the future of decentralized online shopping. Earn $DAN crypto tokens with every purchase, unlock exclusive perks, and own your commerce journey. Join the revolution!">
    <meta name="keywords" content="Nashop, DAN token, $DAN, decentralized shopping, crypto rewards, web3 commerce, blockchain shopping, online shopping, cryptocurrency, NFT marketplace, digital ownership">

    <link rel="canonical" href="https://www.nashop.store/" />
    <meta property="og:title" content="Nashop: The Future of Decentralized Shopping | Earn $DAN Tokens" />
    <meta property="og:description" content="Earn $DAN crypto tokens with every purchase on <PERSON><PERSON>, unlock exclusive perks, and own your decentralized commerce journey." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.nashop.store/" />
    <meta property="og:image" content="https://www.nashop.store/img/nashop-og-image.png" /> <!-- Make sure this image exists -->
    <meta property="og:site_name" content="Nashop" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@nashopstoreweb3" />
    <meta name="twitter:creator" content="@nashopstoreweb3" />
    <meta name="twitter:title" content="Nashop: The Future of Decentralized Shopping | Earn $DAN Tokens" />
    <meta name="twitter:description" content="Earn $DAN crypto tokens with every purchase on Nashop, unlock exclusive perks, and own your decentralized commerce journey." />
    <meta name="twitter:image" content="https://www.nashop.store/img/nashop-twitter-image.png" /> <!-- Make sure this image exists -->

    <!-- Favicons - Using available logo -->
    <link rel="icon" type="image/x-icon" href="SHOP-LOGO-BLACK.ico">
    <!-- Removed missing favicon files to prevent 404 errors -->

    <!-- External Stylesheets & Fonts -->
    <!-- Bootstrap 5 CSS for optimized animations and responsive components -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Tailwind CSS CDN (for development only) -->
    <!-- TODO: For production, install Tailwind CSS as PostCSS plugin or use Tailwind CLI -->
    <!-- See: https://tailwindcss.com/docs/installation -->
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Link to your custom CSS file -->
    <link rel="stylesheet" href="css/style.css">

    <!-- External Scripts (defer loading) -->
    <script defer src="https://unpkg.com/lucide@latest"></script>

    <!-- Bootstrap 5 JavaScript Bundle (includes Popper) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Custom JavaScript Files -->
    <script defer src="js/countdown.js"></script>
    <script defer src="js/comparison-table.js"></script>
    <script defer src="js/script.js"></script>
    <script defer src="js/menu.js"></script>
    <script defer src="js/qr-notification-handler.js"></script>

    <!-- Performance Testing Suite (only in development) -->
    <script defer src="js/performance-test.js"></script>

    <!-- Custom styles moved to external CSS file -->

    <!-- Schema.org Markup -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Nashop",
      "url": "https://www.nashop.store/",
      "description": "Nashop is a decentralized online shopping platform rewarding users with $DAN crypto tokens for purchases.",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.nashop.store/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

</head>
<body class="bg-black text-gray-200 font-sans selection:bg-fuchsia-500/40 selection:text-white overflow-x-hidden antialiased">

    <!-- Revolutionary Hero Section - Ultra Dark Immersive Experience -->
    <header id="top" class="relative min-h-screen flex items-center justify-center overflow-hidden text-center px-4 md:px-6 py-20 z-0 bg-black" role="banner">
        <!-- Simplified Dark Background -->
        <div class="absolute inset-0 -z-10 overflow-hidden bg-black" aria-hidden="true">
            <!-- Subtle Background Gradients -->
            <div class="absolute -top-1/4 -left-1/4 w-full h-full bg-gradient-to-br from-purple-900/15 via-black to-black opacity-60"></div>
            <div class="absolute -bottom-1/4 -right-1/4 w-full h-full bg-gradient-to-tl from-cyan-900/10 via-black to-black opacity-50"></div>

            <!-- Minimal Star Field -->
            <div class="stars-container absolute inset-0 opacity-60">
                <!-- Simple Stars -->
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[15%] left-[10%] animate-twinkle" style="animation-delay: 0s; animation-duration: 6s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[40%] left-[80%] animate-twinkle" style="animation-delay: 2s; animation-duration: 8s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[85%] left-[75%] animate-twinkle" style="animation-delay: 4s; animation-duration: 7s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[30%] left-[60%] animate-twinkle" style="animation-delay: 1s; animation-duration: 9s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[60%] left-[30%] animate-twinkle" style="animation-delay: 3s; animation-duration: 5s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[20%] left-[85%] animate-twinkle" style="animation-delay: 5s; animation-duration: 6s;"></div>
            </div>

            <!-- Single Subtle Shooting Star -->
            <div class="shooting-star absolute w-[120px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[25%] left-[-10%] rotate-[15deg] animate-shooting-star opacity-40" style="animation-delay: 10s; animation-duration: 4s;"></div>

            <!-- Minimal Grid Pattern -->
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.02%22%20%2F%3E%3C%2Fsvg%3E')] opacity-30"></div>
        </div>

        <!-- Enhanced Main Content with Modern Animations -->
        <div class="relative z-10 perspective-1000">
            <div class="animate-fade-in transform-gpu will-change-transform" data-animate="fade-in" data-delay="200">
                <!-- Enhanced Logo System -->
                <div class="logo-container inline-block mb-12 relative hover-scale cursor-pointer mx-auto group" data-animate="scale-in" data-delay="400">
                    <div class="logo-3d-container relative transform-gpu transition-transform duration-500 hover:rotate-y-6">

                        <!-- Single Orbital Ring with Enhanced Animation -->
                        <div class="absolute inset-0 w-full h-full">
                            <div class="absolute rounded-full border border-cyan-400/40 animate-spin-slow opacity-60 pulse-glow" style="width: calc(100% + 40px); height: calc(100% + 40px); top: -20px; left: -20px; animation-duration: 20s;"></div>
                        </div>

                        <!-- Central Logo with Enhanced Effects -->
                        <div class="relative fingerprint-container">
                            <!-- Enhanced Background Glow -->
                            <div class="absolute -inset-4 bg-gradient-to-br from-cyan-500/20 via-purple-500/10 to-fuchsia-500/20 rounded-full blur-xl opacity-60 animate-pulse duration-[5000ms] group-hover:opacity-80 transition-all duration-500 will-change-opacity"></div>

                            <!-- Interactive Fingerprint Core (PRESERVED FUNCTIONALITY) -->
                            <div class="fingerprint-clickable relative cursor-pointer group/finger hover-glow" aria-label="Nashop Logo - Click to watch whitelist video" role="button" tabindex="0">
                                <!-- Main Fingerprint Icon -->
                                <i data-lucide="fingerprint" class="w-20 h-20 text-cyan-300 drop-shadow-[0_0_15px_rgba(34,211,238,0.5)] transition-all duration-500 group-hover/finger:text-white group-hover/finger:drop-shadow-[0_0_25px_rgba(255,255,255,0.7)] group-hover/finger:scale-110 will-change-transform" aria-label="Nashop logo icon"></i>

                                <!-- Enhanced Pulse Ring -->
                                <div class="absolute inset-0 rounded-full border border-cyan-300/30 animate-ping-slow opacity-60 group-hover/finger:border-white/50 will-change-opacity"></div>
                            </div>

                            <!-- Particle Effects (PRESERVED for click interactions) -->
                            <div class="fingerprint-particles absolute inset-0 pointer-events-none"></div>
                            <div class="fingerprint-ripple-container absolute inset-0 pointer-events-none"></div>
                            <div class="fingerprint-energy-burst absolute inset-0 pointer-events-none opacity-0"></div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Title System with Modern Animations -->
                <div class="title-container relative mb-16 max-w-6xl mx-auto" data-animate="fade-in" data-delay="600">
                    <!-- Enhanced Background Glow -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-purple-500/10 via-fuchsia-500/10 to-cyan-500/10 rounded-3xl blur-xl opacity-50 will-change-opacity"></div>

                    <!-- Animated Title -->
                    <h1 class="text-6xl md:text-8xl lg:text-9xl font-black uppercase tracking-tighter relative z-10">
                        <!-- Main Title with Enhanced Animation -->
                        <div class="relative inline-block" data-animate="slide-up" data-delay="800">
                            <!-- Main Title Text with Gradient Animation -->
                            <span class="relative block animate-gradient-text will-change-transform">
                                NASHOP
                            </span>
                        </div>

                        <!-- Animated Subtitle -->
                        <div class="relative mt-6 text-2xl md:text-4xl lg:text-5xl font-bold tracking-wide stagger-children" data-animate="stagger" data-delay="1000" data-stagger-delay="150">
                            <span class="relative block">
                                <span class="inline-block bg-gradient-to-r from-purple-300 to-cyan-300 text-transparent bg-clip-text will-change-transform">
                                    THE FUTURE OF
                                </span>
                                <br>
                                <span class="inline-block bg-gradient-to-r from-cyan-300 to-purple-300 text-transparent bg-clip-text will-change-transform">
                                    DECENTRALIZED COMMERCE
                                </span>
                            </span>
                        </div>
                    </h1>
                </div>

                <!-- Enhanced Description with Modern Animations -->
                <div class="description-container relative mb-16 max-w-3xl mx-auto" data-animate="fade-in" data-delay="1200">
                    <div class="relative z-10 text-center">
                        <!-- Animated Main Description -->
                        <p class="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed relative mb-8 animate-fade-in will-change-transform" data-animate="slide-up" data-delay="1400">
                            Step into the future of decentralized commerce. Earn
                            <strong class="relative inline-block mx-2 hover-glow">
                                <!-- Enhanced Token Highlight with Animation -->
                                <span class="absolute inset-0 bg-gradient-to-r from-fuchsia-500/20 to-purple-500/20 rounded-md blur-sm will-change-opacity"></span>
                                <span class="relative text-fuchsia-300 font-semibold animate-gradient-text" data-typewriter data-typewriter-speed="100">$DAN</span>
                            </strong>
                            tokens, unlock exclusive perks, and own your digital shopping journey.
                        </p>

                        <!-- Enhanced Divider with Animation -->
                        <div class="w-24 h-1 bg-gradient-to-r from-purple-500/60 via-fuchsia-500/60 to-cyan-500/60 mx-auto rounded-full animate-fade-in will-change-transform" data-animate="scale-in" data-delay="1600"></div>
                    </div>
                </div>

                <!-- Enhanced CTA Buttons with Modern Animations -->
                <div class="cta-container flex flex-col sm:flex-row gap-5 items-center justify-center max-w-4xl mx-auto stagger-children" data-animate="stagger" data-delay="1800" data-stagger-delay="200">

                    <!-- Enhanced Primary CTA -->
                    <a href="#features"
                       class="btn-enhanced group relative inline-flex items-center justify-center px-6 py-3 rounded-lg font-bold text-base text-white overflow-hidden focus:outline-none focus:ring-2 focus:ring-fuchsia-500/50 w-full sm:w-auto will-change-transform hover-glow">

                        <!-- Enhanced button background -->
                        <span class="absolute inset-0 bg-gradient-to-r from-purple-600 to-fuchsia-600 group-hover:from-purple-700 group-hover:to-fuchsia-700 transition-all duration-300 will-change-opacity"></span>

                        <!-- Enhanced border -->
                        <span class="absolute inset-0 rounded-lg border border-white/10 group-hover:border-white/20 transition-all duration-300"></span>

                        <!-- Button content -->
                        <span class="relative flex items-center justify-center">
                            <span class="text-white">Explore Features</span>
                            <i class="fas fa-arrow-down text-xs ml-2 group-hover:translate-y-1 transition-transform duration-300"></i>
                        </span>
                    </a>

                    <!-- Enhanced Token CTA -->
                    <a href="https://token.nashop.store" target="_blank" rel="noopener noreferrer"
                       class="btn-enhanced group relative inline-flex items-center justify-center px-6 py-3 rounded-lg font-bold text-base text-white overflow-hidden focus:outline-none focus:ring-2 focus:ring-cyan-500/50 w-full sm:w-auto will-change-transform hover-glow">

                        <!-- Enhanced button background -->
                        <span class="absolute inset-0 bg-gradient-to-r from-cyan-600 to-blue-600 group-hover:from-cyan-700 group-hover:to-blue-700 transition-all duration-300 will-change-opacity"></span>

                        <!-- Enhanced border -->
                        <span class="absolute inset-0 rounded-lg border border-white/10 group-hover:border-white/20 transition-all duration-300"></span>

                        <!-- Button content -->
                        <span class="relative flex items-center justify-center">
                            <span class="text-white">Get $DAN Token</span>
                            <i class="fas fa-external-link-alt text-xs ml-2 group-hover:scale-110 transition-transform duration-300"></i>
                        </span>
                    </a>

                    <!-- Enhanced Documentation CTA -->
                    <a href="/nashop_doc_beta" target="_blank" rel="noopener noreferrer"
                       class="btn-enhanced group relative inline-flex items-center justify-center px-6 py-3 rounded-lg font-bold text-base text-gray-200 overflow-hidden focus:outline-none focus:ring-2 focus:ring-gray-500/50 w-full sm:w-auto will-change-transform hover-glow">

                        <!-- Enhanced button background with glass effect -->
                        <span class="absolute inset-0 bg-gray-800/80 backdrop-blur-sm group-hover:bg-gray-700/80 transition-all duration-300 will-change-opacity"></span>

                        <!-- Enhanced border -->
                        <span class="absolute inset-0 rounded-lg border border-gray-700 group-hover:border-gray-600 transition-all duration-300"></span>

                        <!-- Button content -->
                        <span class="relative flex items-center justify-center">
                            <span class="text-gray-200">Read Documentation</span>
                            <i class="fas fa-external-link-alt text-xs ml-2 group-hover:scale-110 transition-transform duration-300"></i>
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Scroll Indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center" data-animate="fade-in" data-delay="2200">
            <!-- Enhanced arrow design -->
            <a href="#features" aria-label="Scroll down to features" class="group relative flex items-center justify-center w-10 h-10 hover-lift">
                <!-- Enhanced background glow on hover -->
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-cyan-500/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 will-change-opacity"></div>

                <!-- Enhanced arrow button -->
                <div class="relative w-10 h-10 flex items-center justify-center rounded-full border border-gray-700/50 bg-gray-900/40 backdrop-blur-sm group-hover:border-cyan-500/50 transition-all duration-300 animate-float will-change-transform">
                    <i data-lucide="arrow-down" class="w-4 h-4 text-gray-300 group-hover:text-cyan-300 transition-colors duration-300 group-hover:translate-y-1 will-change-transform"></i>
                </div>
            </a>

            <!-- Enhanced text label -->
            <span class="text-xs text-gray-400 font-medium mt-2 group-hover:text-gray-300 transition-colors duration-300 will-change-opacity">Scroll Down</span>
        </div>
    </header>

    <!-- Quantum Video Modal for Whitelist -->
    <div id="whitelist-video-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-md opacity-0 pointer-events-none transition-all duration-700 ease-out">
        <!-- Modal Background Effects -->
        <div class="absolute inset-0 overflow-hidden">
            <!-- Quantum Energy Field -->
            <div class="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-gradient-radial from-purple-900/20 via-black to-black animate-spin-ultra-slow opacity-60"></div>
            <div class="absolute -bottom-1/2 -right-1/2 w-[200%] h-[200%] bg-gradient-radial from-cyan-900/15 via-black to-black animate-spin-ultra-slow-reverse opacity-50"></div>

            <!-- Matrix Grid -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute left-[20%] top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-purple-500/30 to-transparent animate-pulse duration-[8000ms]"></div>
                <div class="absolute left-[40%] top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-cyan-500/20 to-transparent animate-pulse duration-[12000ms]"></div>
                <div class="absolute left-[60%] top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-fuchsia-500/25 to-transparent animate-pulse duration-[10000ms]"></div>
                <div class="absolute left-[80%] top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-purple-500/20 to-transparent animate-pulse duration-[14000ms]"></div>

                <div class="absolute top-[25%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-500/25 to-transparent animate-pulse duration-[7000ms]"></div>
                <div class="absolute top-[50%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent animate-pulse duration-[13000ms]"></div>
                <div class="absolute top-[75%] left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-fuchsia-500/20 to-transparent animate-pulse duration-[9500ms]"></div>
            </div>

            <!-- Floating Particles -->
            <div class="absolute inset-0 opacity-60">
                <div class="absolute top-[20%] left-[15%] w-2 h-2 bg-purple-400/60 rounded-full animate-float-slow"></div>
                <div class="absolute top-[60%] right-[20%] w-1.5 h-1.5 bg-cyan-400/60 rounded-full animate-float-slow-reverse"></div>
                <div class="absolute bottom-[30%] left-[70%] w-2 h-2 bg-fuchsia-400/60 rounded-full animate-float-slow"></div>
            </div>
        </div>

        <!-- Modal Content Container -->
        <div class="modal-content relative max-w-6xl w-full mx-4 transform scale-90 transition-transform duration-700 ease-out">
            <!-- Close Button -->
            <button id="close-video-modal" class="absolute -top-4 -right-4 z-10 w-12 h-12 flex items-center justify-center rounded-full bg-gray-900/80 backdrop-blur-sm border-2 border-gray-600/40 hover:border-red-500/60 text-gray-400 hover:text-red-400 transition-all duration-300 group">
                <div class="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <i class="fas fa-times text-xl relative z-10"></i>
                <span class="sr-only">Close video</span>
            </button>

            <!-- Video Container with Quantum Frame -->
            <div class="relative rounded-3xl overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border-2 border-gray-700/50 shadow-2xl">
                <!-- Quantum Border Glow -->
                <div class="absolute -inset-1 bg-gradient-to-r from-purple-500/30 via-cyan-500/30 to-fuchsia-500/30 rounded-3xl blur-xl opacity-70 animate-pulse duration-[5000ms]"></div>

                <!-- Video Content Area -->
                <div class="relative bg-black rounded-3xl overflow-hidden">
                    <!-- Video Element -->
                    <video id="whitelist-video" class="w-full h-auto max-h-[70vh] object-contain" controls preload="metadata" poster="">
                        <source src="img/QR VID.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>

                    <!-- Video Loading Overlay -->
                    <div id="video-loading" class="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
                        <div class="text-center">
                            <div class="w-16 h-16 border-4 border-cyan-500/30 border-t-cyan-500 rounded-full animate-spin mx-auto mb-4"></div>
                            <p class="text-white text-lg font-medium">Loading Quantum Experience...</p>
                        </div>
                    </div>
                </div>

                <!-- Information Panel -->
                <div class="relative p-8 bg-gradient-to-br from-gray-900/90 via-gray-800/90 to-gray-900/90 backdrop-blur-sm">
                    <!-- Quantum Divider -->
                    <div class="absolute top-0 left-8 right-8 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent"></div>

                    <div class="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                        <!-- Left Side: Title and Description -->
                        <div class="flex-1">
                            <h3 class="text-2xl md:text-3xl font-bold mb-3">
                                <span class="bg-gradient-to-r from-purple-300 via-cyan-300 to-fuchsia-300 text-transparent bg-clip-text animate-gradient-x">
                                    Join the Nashop Quantum Whitelist
                                </span>
                            </h3>
                            <p class="text-gray-300 text-lg leading-relaxed mb-4">
                                Discover how to secure your spot in the future of decentralized commerce. Learn about exclusive early access benefits,
                                <span class="text-fuchsia-300 font-semibold">$DAN token rewards</span>, and be among the first to experience the quantum leap in online shopping.
                            </p>

                            <!-- Feature Highlights -->
                            <div class="flex flex-wrap gap-3">
                                <div class="flex items-center space-x-2 text-sm">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                                    <span class="text-purple-300 font-medium">Early Access</span>
                                </div>
                                <div class="flex items-center space-x-2 text-sm">
                                    <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                                    <span class="text-cyan-300 font-medium">Exclusive Rewards</span>
                                </div>
                                <div class="flex items-center space-x-2 text-sm">
                                    <div class="w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                                    <span class="text-fuchsia-300 font-medium">VIP Benefits</span>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side: Action Button -->
                        <div class="lg:flex-shrink-0">
                            <a href="#qr-code-section" id="join-whitelist-cta" class="group relative inline-flex items-center justify-center px-8 py-4 rounded-2xl font-bold text-lg text-white overflow-hidden transition-all duration-500 ease-out transform hover:scale-105">
                                <!-- Button Background -->
                                <span class="absolute inset-0 bg-gradient-to-r from-purple-600 via-fuchsia-600 to-cyan-600 group-hover:from-purple-500 group-hover:via-fuchsia-500 group-hover:to-cyan-500 transition-all duration-500 animate-gradient-x"></span>

                                <!-- Button Border -->
                                <span class="absolute inset-0 rounded-2xl border-2 border-white/20 group-hover:border-white/40 transition-all duration-500 shadow-[0_0_20px_rgba(168,85,247,0.4)] group-hover:shadow-[0_0_30px_rgba(168,85,247,0.6)]"></span>

                                <!-- Button Content -->
                                <span class="relative flex items-center space-x-3">
                                    <i data-lucide="zap" class="w-5 h-5 text-white group-hover:animate-pulse"></i>
                                    <span class="text-white font-bold tracking-wide">JOIN WHITELIST NOW</span>
                                    <i class="fas fa-arrow-right text-sm group-hover:translate-x-1 transition-transform duration-300"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Coming Soon Countdown Section -->
    <section id="coming-soon" class="py-20 md:py-28 bg-gradient-to-b from-black via-purple-950/10 to-black relative overflow-hidden">
        <!-- Enhanced Background Elements -->
        <div class="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
            <!-- Enhanced background gradients with animation -->
            <div class="absolute top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-fuchsia-600/20 via-purple-600/15 to-transparent rounded-full blur-[120px] opacity-60 translate-x-1/4 -translate-y-1/4 animate-pulse duration-[15000ms]"></div>
            <div class="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-cyan-600/20 via-blue-600/15 to-transparent rounded-full blur-[120px] opacity-60 -translate-x-1/4 translate-y-1/4 animate-pulse duration-[18000ms]"></div>

            <!-- Additional cosmic elements -->
            <div class="absolute top-1/3 left-1/4 w-32 h-32 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-[50px] animate-float-slow" style="animation-duration: 25s;"></div>
            <div class="absolute bottom-1/4 right-1/3 w-40 h-40 bg-gradient-to-tl from-cyan-500/10 to-transparent rounded-full blur-[60px] animate-float-slow-reverse" style="animation-duration: 30s;"></div>

            <!-- Subtle grid pattern overlay -->
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10"></div>

            <!-- Enhanced animated star field effect -->
            <div class="stars-container absolute inset-0 opacity-70">
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[25%] left-[30%] animate-twinkle" style="animation-delay: 0.2s; animation-duration: 3.5s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[45%] left-[70%] animate-twinkle" style="animation-delay: 1.2s; animation-duration: 4.5s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[65%] left-[20%] animate-twinkle" style="animation-delay: 2.1s; animation-duration: 3.8s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[15%] left-[80%] animate-twinkle" style="animation-delay: 1.5s; animation-duration: 4.2s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[85%] left-[40%] animate-twinkle" style="animation-delay: 0.8s; animation-duration: 4.0s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[35%] left-[15%] animate-twinkle" style="animation-delay: 2.5s; animation-duration: 5.0s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[55%] left-[85%] animate-twinkle" style="animation-delay: 1.8s; animation-duration: 3.2s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[75%] left-[60%] animate-twinkle" style="animation-delay: 3.0s; animation-duration: 4.8s;"></div>
            </div>

            <!-- Shooting stars -->
            <div class="shooting-star absolute w-[100px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[20%] left-[-10%] rotate-[15deg] animate-shooting-star" style="animation-delay: 3s;"></div>
            <div class="shooting-star absolute w-[150px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[40%] left-[-15%] rotate-[25deg] animate-shooting-star" style="animation-delay: 7s;"></div>
            <div class="shooting-star absolute w-[80px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[70%] left-[-8%] rotate-[10deg] animate-shooting-star" style="animation-delay: 11s;"></div>

            <!-- Animated nebula/cosmic dust effect -->
            <div class="absolute inset-0 opacity-30 overflow-hidden">
                <div class="cosmic-dust absolute top-1/4 left-1/3 w-[150px] h-[150px] bg-gradient-to-br from-purple-500/20 via-fuchsia-500/10 to-transparent rounded-full blur-[30px] animate-float-slow" style="animation-duration: 25s;"></div>
                <div class="cosmic-dust absolute bottom-1/3 right-1/4 w-[200px] h-[200px] bg-gradient-to-tl from-cyan-500/20 via-blue-500/10 to-transparent rounded-full blur-[40px] animate-float-slow-reverse" style="animation-duration: 30s;"></div>
                <div class="cosmic-dust absolute top-2/3 left-2/3 w-[120px] h-[120px] bg-gradient-to-tr from-fuchsia-500/15 via-purple-500/10 to-transparent rounded-full blur-[25px] animate-float-slow" style="animation-duration: 20s;"></div>
            </div>

            <!-- Digital circuit lines -->
            <div class="absolute inset-0 opacity-20">
                <!-- Horizontal lines -->
                <div class="absolute top-1/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
                <div class="absolute top-2/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-fuchsia-500/30 to-transparent"></div>
                <div class="absolute top-3/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent"></div>

                <!-- Vertical lines -->
                <div class="absolute left-1/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-purple-500/30 to-transparent"></div>
                <div class="absolute left-2/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-fuchsia-500/30 to-transparent"></div>
                <div class="absolute left-3/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-cyan-500/30 to-transparent"></div>

                <!-- Animated data points -->
                <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400/50 rounded-full animate-pulse"></div>
                <div class="absolute top-2/4 left-2/4 w-2 h-2 bg-fuchsia-400/50 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute top-3/4 left-3/4 w-2 h-2 bg-cyan-400/50 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
            </div>
        </div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <div class="text-center mb-10" data-animate="fade-in" data-delay="200">
                <!-- Enhanced heading with modern animations -->
                <div class="relative inline-block mb-4" data-animate="scale-in" data-delay="400">
                    <div class="absolute -inset-4 bg-gradient-to-r from-purple-500/20 via-fuchsia-500/20 to-cyan-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[4000ms] will-change-opacity" aria-hidden="true"></div>
                    <h2 class="relative text-4xl md:text-5xl lg:text-6xl font-bold animate-gradient-text uppercase tracking-tighter will-change-transform">
                        Coming 2 Live
                    </h2>
                </div>
                <p class="text-gray-300 text-lg md:text-xl opacity-90 max-w-3xl mx-auto animate-fade-in will-change-opacity" data-animate="slide-up" data-delay="600">
                    The future of decentralized shopping launches in:
                </p>
                <!-- Enhanced decorative divider -->
                <div class="w-24 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-cyan-500 mx-auto mt-6 rounded-full animate-fade-in will-change-transform" data-animate="scale-in" data-delay="800"></div>
            </div>

            <!-- Enhanced Countdown Timer with Modern Animations -->
            <div class="max-w-5xl mx-auto" data-animate="fade-in" data-delay="1000">

                <div class="countdown-container grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 text-center stagger-children" data-animate="stagger" data-delay="1200" data-stagger-delay="150">
                    <!-- Enhanced Days Counter -->
                    <div class="countdown-item countdown-number group will-change-transform">
                        <div class="relative">
                            <!-- Enhanced background glow -->
                            <div class="absolute -inset-3 bg-gradient-to-br from-purple-600/20 via-fuchsia-600/10 to-transparent rounded-full blur-xl opacity-70 group-hover:opacity-100 group-hover:from-purple-600/30 transition-all duration-500"></div>

                            <!-- Circular progress indicator -->
                            <div class="absolute -inset-2 flex items-center justify-center">
                                <svg class="w-full h-full" viewBox="0 0 100 100">
                                    <circle class="text-gray-800/50" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                                    <circle class="text-purple-500/40" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" stroke-dasharray="283" stroke-dashoffset="60" />
                                </svg>
                                <!-- Animated dots on the circle -->
                                <div class="absolute top-0 left-1/2 -translate-x-1/2 w-2 h-2 bg-purple-400/70 rounded-full"></div>
                                <div class="absolute bottom-0 left-1/2 -translate-x-1/2 w-2 h-2 bg-fuchsia-400/70 rounded-full"></div>
                            </div>

                            <!-- Main counter box with enhanced 3D and glass effect -->
                            <div class="relative p-6 bg-gray-900/60 backdrop-blur-md border border-purple-500/30 rounded-xl group-hover:border-purple-500/50 transition-all duration-300 shadow-lg group-hover:shadow-purple-500/30 transform group-hover:translate-y-[-4px] group-hover:scale-[1.02]">
                                <!-- Flip animation container -->
                                <div class="flip-card-container h-16 md:h-20 relative perspective">
                                    <div id="days-flip" class="flip-card relative w-full h-full preserve-3d transition-transform duration-500">
                                        <div class="flip-card-front absolute inset-0 backface-hidden flex items-center justify-center">
                                            <div id="days" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-300 to-fuchsia-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                        <div class="flip-card-back absolute inset-0 backface-hidden rotate-x-180 flex items-center justify-center">
                                            <div id="days-next" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-300 to-fuchsia-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Label with enhanced styling -->
                                <div class="text-gray-300 text-sm md:text-base mt-3 uppercase tracking-wider font-medium">
                                    <span class="bg-gradient-to-r from-purple-300 to-fuchsia-300 text-transparent bg-clip-text">Days</span>
                                </div>

                                <!-- Decorative elements -->
                                <div class="absolute top-2 right-2 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-70"></div>
                                <div class="absolute bottom-2 left-2 w-1 h-1 bg-fuchsia-400 rounded-full animate-pulse opacity-70"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Hours Counter with flip animation and circular progress -->
                    <div class="countdown-item group">
                        <div class="relative">
                            <!-- Enhanced background glow -->
                            <div class="absolute -inset-3 bg-gradient-to-br from-fuchsia-600/20 via-purple-600/10 to-transparent rounded-full blur-xl opacity-70 group-hover:opacity-100 group-hover:from-fuchsia-600/30 transition-all duration-500"></div>

                            <!-- Circular progress indicator -->
                            <div class="absolute -inset-2 flex items-center justify-center">
                                <svg class="w-full h-full" viewBox="0 0 100 100">
                                    <circle class="text-gray-800/50" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                                    <circle class="text-fuchsia-500/40" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" stroke-dasharray="283" stroke-dashoffset="140" />
                                </svg>
                                <!-- Animated dots on the circle -->
                                <div class="absolute top-1/4 right-0 w-2 h-2 bg-fuchsia-400/70 rounded-full"></div>
                                <div class="absolute bottom-1/4 left-0 w-2 h-2 bg-purple-400/70 rounded-full"></div>
                            </div>

                            <!-- Main counter box with enhanced 3D and glass effect -->
                            <div class="relative p-6 bg-gray-900/60 backdrop-blur-md border border-fuchsia-500/30 rounded-xl group-hover:border-fuchsia-500/50 transition-all duration-300 shadow-lg group-hover:shadow-fuchsia-500/30 transform group-hover:translate-y-[-4px] group-hover:scale-[1.02]">
                                <!-- Flip animation container -->
                                <div class="flip-card-container h-16 md:h-20 relative perspective">
                                    <div id="hours-flip" class="flip-card relative w-full h-full preserve-3d transition-transform duration-500">
                                        <div class="flip-card-front absolute inset-0 backface-hidden flex items-center justify-center">
                                            <div id="hours" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-fuchsia-300 to-purple-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                        <div class="flip-card-back absolute inset-0 backface-hidden rotate-x-180 flex items-center justify-center">
                                            <div id="hours-next" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-fuchsia-300 to-purple-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Label with enhanced styling -->
                                <div class="text-gray-300 text-sm md:text-base mt-3 uppercase tracking-wider font-medium">
                                    <span class="bg-gradient-to-r from-fuchsia-300 to-purple-300 text-transparent bg-clip-text">Hours</span>
                                </div>

                                <!-- Decorative elements -->
                                <div class="absolute top-2 left-2 w-1 h-1 bg-fuchsia-400 rounded-full animate-ping opacity-70"></div>
                                <div class="absolute bottom-2 right-2 w-1 h-1 bg-purple-400 rounded-full animate-pulse opacity-70"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Minutes Counter with flip animation and circular progress -->
                    <div class="countdown-item group">
                        <div class="relative">
                            <!-- Enhanced background glow -->
                            <div class="absolute -inset-3 bg-gradient-to-br from-cyan-600/20 via-blue-600/10 to-transparent rounded-full blur-xl opacity-70 group-hover:opacity-100 group-hover:from-cyan-600/30 transition-all duration-500"></div>

                            <!-- Circular progress indicator -->
                            <div class="absolute -inset-2 flex items-center justify-center">
                                <svg class="w-full h-full" viewBox="0 0 100 100">
                                    <circle class="text-gray-800/50" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                                    <circle class="text-cyan-500/40" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" stroke-dasharray="283" stroke-dashoffset="200" />
                                </svg>
                                <!-- Animated dots on the circle -->
                                <div class="absolute top-0 right-1/4 w-2 h-2 bg-cyan-400/70 rounded-full"></div>
                                <div class="absolute bottom-0 right-1/4 w-2 h-2 bg-blue-400/70 rounded-full"></div>
                            </div>

                            <!-- Main counter box with enhanced 3D and glass effect -->
                            <div class="relative p-6 bg-gray-900/60 backdrop-blur-md border border-cyan-500/30 rounded-xl group-hover:border-cyan-500/50 transition-all duration-300 shadow-lg group-hover:shadow-cyan-500/30 transform group-hover:translate-y-[-4px] group-hover:scale-[1.02]">
                                <!-- Flip animation container -->
                                <div class="flip-card-container h-16 md:h-20 relative perspective">
                                    <div id="minutes-flip" class="flip-card relative w-full h-full preserve-3d transition-transform duration-500">
                                        <div class="flip-card-front absolute inset-0 backface-hidden flex items-center justify-center">
                                            <div id="minutes" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cyan-300 to-blue-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                        <div class="flip-card-back absolute inset-0 backface-hidden rotate-x-180 flex items-center justify-center">
                                            <div id="minutes-next" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cyan-300 to-blue-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Label with enhanced styling -->
                                <div class="text-gray-300 text-sm md:text-base mt-3 uppercase tracking-wider font-medium">
                                    <span class="bg-gradient-to-r from-cyan-300 to-blue-300 text-transparent bg-clip-text">Minutes</span>
                                </div>

                                <!-- Decorative elements -->
                                <div class="absolute top-2 right-2 w-1 h-1 bg-cyan-400 rounded-full animate-ping opacity-70"></div>
                                <div class="absolute bottom-2 left-2 w-1 h-1 bg-blue-400 rounded-full animate-pulse opacity-70"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Seconds Counter with flip animation and circular progress -->
                    <div class="countdown-item group">
                        <div class="relative">
                            <!-- Enhanced background glow -->
                            <div class="absolute -inset-3 bg-gradient-to-br from-blue-600/20 via-cyan-600/10 to-transparent rounded-full blur-xl opacity-70 group-hover:opacity-100 group-hover:from-blue-600/30 transition-all duration-500"></div>

                            <!-- Circular progress indicator that rotates with seconds -->
                            <div class="absolute -inset-2 flex items-center justify-center">
                                <svg class="w-full h-full" viewBox="0 0 100 100">
                                    <circle class="text-gray-800/50" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                                    <circle id="seconds-circle" class="text-blue-500/40" stroke-width="2" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" stroke-dasharray="283" stroke-dashoffset="0" />
                                </svg>
                                <!-- Animated dots on the circle -->
                                <div class="absolute top-1/2 right-0 w-2 h-2 bg-blue-400/70 rounded-full"></div>
                                <div class="absolute top-1/2 left-0 w-2 h-2 bg-cyan-400/70 rounded-full"></div>
                            </div>

                            <!-- Main counter box with enhanced 3D and glass effect -->
                            <div class="relative p-6 bg-gray-900/60 backdrop-blur-md border border-blue-500/30 rounded-xl group-hover:border-blue-500/50 transition-all duration-300 shadow-lg group-hover:shadow-blue-500/30 transform group-hover:translate-y-[-4px] group-hover:scale-[1.02]">
                                <!-- Flip animation container -->
                                <div class="flip-card-container h-16 md:h-20 relative perspective">
                                    <div id="seconds-flip" class="flip-card relative w-full h-full preserve-3d transition-transform duration-500">
                                        <div class="flip-card-front absolute inset-0 backface-hidden flex items-center justify-center">
                                            <div id="seconds" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-300 to-cyan-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                        <div class="flip-card-back absolute inset-0 backface-hidden rotate-x-180 flex items-center justify-center">
                                            <div id="seconds-next" class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-300 to-cyan-300 text-transparent bg-clip-text">00</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Label with enhanced styling -->
                                <div class="text-gray-300 text-sm md:text-base mt-3 uppercase tracking-wider font-medium">
                                    <span class="bg-gradient-to-r from-blue-300 to-cyan-300 text-transparent bg-clip-text">Seconds</span>
                                </div>

                                <!-- Decorative elements -->
                                <div class="absolute top-2 left-2 w-1 h-1 bg-blue-400 rounded-full animate-ping opacity-70"></div>
                                <div class="absolute bottom-2 right-2 w-1 h-1 bg-cyan-400 rounded-full animate-pulse opacity-70"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Target Date Display -->
                <div class="text-center mt-10 mb-6">
                    <div class="inline-block relative">
                        <div class="absolute -inset-1 bg-gradient-to-r from-purple-500/20 via-fuchsia-500/20 to-cyan-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>
                        <div class="relative inline-flex items-center justify-center px-8 py-3 rounded-full text-base font-medium text-white bg-gradient-to-r from-purple-600/40 to-cyan-600/40 border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-[1.02] shadow-lg shadow-purple-900/10">
                            <i class="fas fa-calendar-alt mr-2 text-fuchsia-300"></i>
                            <span class="text-gray-200">Target Launch: <span class="text-fuchsia-300 font-mono font-bold">January 5, 2026</span></span>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </section>

    <!-- Countdown JavaScript moved to external file -->

    <!-- CSS animations moved to external file -->


    <!-- Enhanced Features Section with Modern Animations -->
    <section id="features" aria-labelledby="features-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-purple-950/15 to-black relative overflow-hidden">
        <!-- Enhanced Background Elements with Parallax -->
        <div class="absolute top-1/4 left-0 w-1/3 h-1/2 bg-gradient-to-br from-fuchsia-600/15 to-transparent rounded-full blur-[120px] opacity-60 -translate-x-1/2 animate-pulse duration-[6000ms] parallax-element will-change-transform" data-parallax-speed="0.3" aria-hidden="true"></div>
        <div class="absolute bottom-1/4 right-0 w-1/3 h-1/2 bg-gradient-to-tl from-cyan-600/15 to-transparent rounded-full blur-[120px] opacity-60 translate-x-1/2 animate-pulse duration-[7000ms] animation-delay-3000 parallax-element will-change-transform" data-parallax-speed="0.2" aria-hidden="true"></div>
        <!-- Enhanced floating particles -->
        <div class="absolute inset-0 opacity-20 overflow-hidden" aria-hidden="true">
            <div class="absolute top-1/3 left-1/4 w-2 h-2 bg-purple-400 rounded-full animate-pulse duration-[4000ms] parallax-element" data-parallax-speed="0.1"></div>
            <div class="absolute top-2/3 left-3/4 w-3 h-3 bg-fuchsia-400 rounded-full animate-pulse duration-[5000ms] parallax-element" data-parallax-speed="0.15"></div>
            <div class="absolute top-1/2 left-1/2 w-2 h-2 bg-cyan-400 rounded-full animate-pulse duration-[6000ms] parallax-element" data-parallax-speed="0.05"></div>
        </div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <!-- Enhanced heading with modern animations -->
                <div class="relative inline-block mb-6" data-animate="scale-in" data-delay="400">
                    <!-- Enhanced background glow with multiple layers -->
                    <div class="absolute -inset-6 bg-gradient-to-r from-purple-500/30 via-fuchsia-500/30 to-cyan-500/30 rounded-2xl blur-2xl opacity-60 animate-pulse duration-[4000ms] will-change-opacity" aria-hidden="true"></div>
                    <div class="absolute -inset-3 bg-gradient-to-r from-purple-400/20 via-fuchsia-400/20 to-cyan-400/20 rounded-xl blur-xl opacity-80 animate-pulse duration-[3000ms] will-change-opacity" aria-hidden="true"></div>

                    <!-- Main heading with enhanced gradient animation -->
                    <h2 id="features-heading" class="relative text-4xl md:text-5xl lg:text-7xl font-black mb-4 uppercase tracking-tighter will-change-transform">
                        <span class="inline-block bg-gradient-to-r from-purple-300 via-fuchsia-300 to-cyan-300 text-transparent bg-clip-text animate-gradient-x">
                            Why Choose
                        </span>
                        <br>
                        <span class="inline-block bg-gradient-to-r from-cyan-300 via-purple-300 to-fuchsia-300 text-transparent bg-clip-text animate-gradient-x whitespace-nowrap" style="animation-delay: 0.5s;">
                            Nashop & $DAN?
                        </span>
                    </h2>

                    <!-- Floating particles around heading -->
                    <div class="absolute -inset-8 pointer-events-none opacity-60">
                        <div class="absolute top-0 left-1/4 w-2 h-2 bg-purple-400 rounded-full animate-float-slow"></div>
                        <div class="absolute top-1/4 right-0 w-1.5 h-1.5 bg-fuchsia-400 rounded-full animate-float-slow-reverse"></div>
                        <div class="absolute bottom-0 right-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-float-slow"></div>
                        <div class="absolute bottom-1/4 left-0 w-1.5 h-1.5 bg-purple-400 rounded-full animate-float-slow-reverse"></div>
                    </div>
                </div>

                <!-- Enhanced subtitle with typewriter effect -->
                <div class="max-w-4xl mx-auto mb-8" data-animate="slide-up" data-delay="600">
                    <p class="text-gray-300 text-xl md:text-2xl lg:text-3xl font-medium leading-relaxed mb-4">
                        <span class="typewriter-text bg-gradient-to-r from-gray-200 to-gray-400 text-transparent bg-clip-text"
                              data-typewriter
                              data-typewriter-speed="50"
                              data-typewriter-delay="800">
                            Experience the revolutionary synergy of shopping and earning
                        </span>
                    </p>
                    <p class="text-gray-400 text-lg md:text-xl opacity-90 leading-relaxed">
                        <span class="typewriter-text"
                              data-typewriter
                              data-typewriter-speed="40"
                              data-typewriter-delay="2500">
                            Where every purchase becomes an investment in your digital future
                        </span>
                    </p>
                </div>

                <!-- Enhanced decorative elements -->
                <div class="flex items-center justify-center space-x-4 mb-6" data-animate="scale-in" data-delay="1000">
                    <div class="w-16 h-1 bg-gradient-to-r from-transparent via-purple-500 to-transparent rounded-full animate-fade-in"></div>
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full animate-pulse"></div>
                    <div class="w-24 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-cyan-500 rounded-full animate-gradient-x"></div>
                    <div class="w-3 h-3 bg-gradient-to-r from-fuchsia-500 to-cyan-500 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                    <div class="w-16 h-1 bg-gradient-to-r from-transparent via-cyan-500 to-transparent rounded-full animate-fade-in"></div>
                </div>

                <!-- Call-to-action stats -->
                <div class="flex flex-wrap justify-center gap-8 text-center" data-animate="stagger" data-delay="1200" data-stagger-delay="200">
                    <div class="group">
                        <div class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 text-transparent bg-clip-text">
                            <span class="counter" data-target="100">0</span>%
                        </div>
                        <div class="text-sm text-gray-400 font-medium">Decentralized</div>
                    </div>
                    <div class="group">
                        <div class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-fuchsia-400 to-cyan-400 text-transparent bg-clip-text">
                            <span class="counter" data-target="24">0</span>/7
                        </div>
                        <div class="text-sm text-gray-400 font-medium">Earning Rewards</div>
                    </div>
                    <div class="group">
                        <div class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 text-transparent bg-clip-text">
                            ∞
                        </div>
                        <div class="text-sm text-gray-400 font-medium">Possibilities</div>
                    </div>
                </div>
            </div>

            <!-- Enhanced grid with modern staggered animations -->
            <div class="grid md:grid-cols-3 gap-8 lg:gap-12 stagger-children" data-animate="stagger" data-delay="1000" data-stagger-delay="200">

                <!-- Feature Pillar 1: Revolutionary Earning System -->
                <div class="feature-pillar-1 feature-card group flex flex-col relative overflow-hidden bg-gradient-to-b from-gray-900/60 to-gray-950/80 border border-purple-800/30 rounded-3xl p-8 lg:p-10 text-center backdrop-blur-sm hover:bg-gradient-to-b hover:from-purple-950/40 hover:to-gray-950/90 hover:border-purple-500/60 transition-all duration-500 ease-out will-change-transform hover:scale-[1.02] hover:-translate-y-2">

                    <!-- Animated background pattern -->
                    <div class="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-500/20 via-transparent to-fuchsia-500/20 animate-gradient-shift"></div>
                        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%3E%3Cpath%20d%3D%22M0%200h60v1H0zM0%2060h60v1H0zM0%200v60h1V0zm60%200v60h1V0z%22%20fill%3D%22%23a855f7%22%20fill-opacity%3D%220.1%22%20%2F%3E%3C%2Fsvg%3E')] animate-pulse duration-[8000ms]"></div>
                    </div>

                    <!-- Enhanced icon container with advanced effects -->
                    <div class="flex justify-center mb-8 relative z-10">
                        <div class="relative">
                            <!-- Multi-layer glow effect -->
                            <div class="absolute -inset-4 bg-purple-500/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                            <div class="absolute -inset-2 bg-purple-400/20 rounded-full blur-lg opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Rotating ring -->
                            <div class="absolute -inset-3 border-2 border-purple-500/30 rounded-full animate-spin-slow group-hover:border-purple-400/60 transition-colors duration-500"></div>

                            <!-- Main icon container -->
                            <div class="relative p-6 bg-gradient-to-br from-purple-600/50 to-fuchsia-600/50 rounded-full ring-2 ring-purple-400/50 shadow-2xl group-hover:scale-110 group-hover:ring-purple-300/70 transition-all duration-500 group-hover:shadow-purple-500/50">
                                <i data-lucide="gem" class="w-12 h-12 text-purple-200 group-hover:text-white transition-colors duration-300 drop-shadow-lg"></i>
                            </div>

                            <!-- Floating particles -->
                            <div class="absolute -inset-6 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                                <div class="absolute top-0 left-1/2 w-1 h-1 bg-purple-400 rounded-full animate-float-up"></div>
                                <div class="absolute bottom-0 right-1/3 w-1 h-1 bg-fuchsia-400 rounded-full animate-float-up" style="animation-delay: 0.3s;"></div>
                                <div class="absolute top-1/2 left-0 w-1 h-1 bg-purple-300 rounded-full animate-float-up" style="animation-delay: 0.6s;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced heading with advanced gradient -->
                    <h3 class="text-3xl lg:text-4xl font-black mb-4 relative z-10">
                        <span class="bg-gradient-to-r from-purple-200 via-fuchsia-200 to-purple-200 text-transparent bg-clip-text animate-gradient-x">
                            Earn Real Crypto Rewards
                        </span>
                    </h3>

                    <!-- Enhanced description with better copy -->
                    <p class="text-gray-300 text-base lg:text-lg leading-relaxed mb-8 relative z-10 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                        Transform every purchase into a <span class="text-purple-300 font-bold">profitable investment</span>.
                        Our revolutionary reward system converts your shopping activity into genuine
                        <strong class="text-purple-200 font-bold bg-purple-500/20 px-2 py-1 rounded-md">$DAN</strong>
                        cryptocurrency tokens that you truly own and control.
                    </p>

                    <!-- Enhanced 3D showcase card -->
                    <div class="relative perspective-container mt-auto group/card">
                        <div class="card-3d-effect preserve-3d relative w-full h-48 md:h-56 rounded-2xl bg-gradient-to-br from-purple-800/60 via-fuchsia-800/50 to-gray-950/70 border-2 border-purple-500/40 shadow-2xl group-hover:scale-[1.03] group-hover:shadow-purple-500/30 transition-all duration-500 flex flex-col items-center justify-center p-6 overflow-hidden group-hover:border-purple-400/60">

                            <!-- Animated background effects -->
                            <div class="absolute inset-0 opacity-40 group-hover:opacity-60 transition-opacity duration-500">
                                <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
                                <div class="absolute bottom-1/3 right-1/3 w-1.5 h-1.5 bg-fuchsia-400 rounded-full animate-pulse duration-[3000ms]"></div>
                                <div class="absolute top-2/3 left-2/3 w-1 h-1 bg-purple-400 rounded-full animate-pulse duration-[4000ms]"></div>
                                <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-fuchsia-300 rounded-full animate-pulse duration-[2500ms]"></div>
                            </div>

                            <!-- Main icon with enhanced effects -->
                            <i data-lucide="coins" class="w-20 h-20 text-purple-300 opacity-70 group-hover:opacity-100 transition-all duration-500 group-hover:scale-110 drop-shadow-lg mb-3"></i>

                            <!-- Value indicator -->
                            <div class="text-center">
                                <div class="text-lg font-bold text-purple-200 mb-1">Real Value</div>
                                <div class="text-sm text-purple-300/80 font-medium">Tradeable • Stakeable • Yours</div>
                            </div>

                            <!-- Corner accent -->
                            <div class="absolute top-3 right-3 flex items-center space-x-1">
                                <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                                <div class="w-1 h-1 bg-fuchsia-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feature Pillar 2: Exclusive VIP Access System -->
                <div class="feature-pillar-2 feature-card group flex flex-col relative overflow-hidden bg-gradient-to-b from-gray-900/60 to-gray-950/80 border border-fuchsia-800/30 rounded-3xl p-8 lg:p-10 text-center backdrop-blur-sm hover:bg-gradient-to-b hover:from-fuchsia-950/40 hover:to-gray-950/90 hover:border-fuchsia-500/60 transition-all duration-500 ease-out will-change-transform hover:scale-[1.02] hover:-translate-y-2">

                    <!-- Animated background pattern -->
                    <div class="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-fuchsia-500/20 via-transparent to-purple-500/20 animate-gradient-shift" style="animation-delay: 1s;"></div>
                        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%3E%3Cpath%20d%3D%22M0%200h60v1H0zM0%2060h60v1H0zM0%200v60h1V0zm60%200v60h1V0z%22%20fill%3D%22%23ec4899%22%20fill-opacity%3D%220.1%22%20%2F%3E%3C%2Fsvg%3E')] animate-pulse duration-[8000ms]"></div>
                    </div>

                    <!-- Enhanced icon container with advanced effects -->
                    <div class="flex justify-center mb-8 relative z-10">
                        <div class="relative">
                            <!-- Multi-layer glow effect -->
                            <div class="absolute -inset-4 bg-fuchsia-500/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                            <div class="absolute -inset-2 bg-fuchsia-400/20 rounded-full blur-lg opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Rotating ring with key pattern -->
                            <div class="absolute -inset-3 border-2 border-fuchsia-500/30 rounded-full animate-spin-slow group-hover:border-fuchsia-400/60 transition-colors duration-500" style="animation-direction: reverse;"></div>

                            <!-- Main icon container -->
                            <div class="relative p-6 bg-gradient-to-br from-fuchsia-600/50 to-purple-600/50 rounded-full ring-2 ring-fuchsia-400/50 shadow-2xl group-hover:scale-110 group-hover:ring-fuchsia-300/70 transition-all duration-500 group-hover:shadow-fuchsia-500/50">
                                <i data-lucide="key-round" class="w-12 h-12 text-fuchsia-200 group-hover:text-white transition-colors duration-300 drop-shadow-lg"></i>
                            </div>

                            <!-- Floating key particles -->
                            <div class="absolute -inset-6 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                                <div class="absolute top-0 right-1/3 w-1 h-1 bg-fuchsia-400 rounded-full animate-float-up"></div>
                                <div class="absolute bottom-0 left-1/4 w-1 h-1 bg-purple-400 rounded-full animate-float-up" style="animation-delay: 0.3s;"></div>
                                <div class="absolute top-1/2 right-0 w-1 h-1 bg-fuchsia-300 rounded-full animate-float-up" style="animation-delay: 0.6s;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced heading with advanced gradient -->
                    <h3 class="text-3xl lg:text-4xl font-black mb-4 relative z-10">
                        <span class="bg-gradient-to-r from-fuchsia-200 via-purple-200 to-fuchsia-200 text-transparent bg-clip-text animate-gradient-x">
                            Unlock Elite VIP Status
                        </span>
                    </h3>

                    <!-- Enhanced description with better copy -->
                    <p class="text-gray-300 text-base lg:text-lg leading-relaxed mb-8 relative z-10 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                        Your <span class="text-fuchsia-200 font-bold bg-fuchsia-500/20 px-2 py-1 rounded-md">$DAN</span> tokens
                        aren't just currency—they're your <span class="text-fuchsia-300 font-bold">master key</span> to an exclusive ecosystem.
                        Access tiered benefits, early product launches, limited editions, and premium platform features that money can't buy.
                    </p>

                    <!-- Enhanced 3D showcase card -->
                    <div class="relative perspective-container mt-auto group/card">
                        <div class="card-3d-effect preserve-3d relative w-full h-48 md:h-56 rounded-2xl bg-gradient-to-br from-fuchsia-800/60 via-purple-800/50 to-gray-950/70 border-2 border-fuchsia-500/40 shadow-2xl group-hover:scale-[1.03] group-hover:shadow-fuchsia-500/30 transition-all duration-500 flex flex-col items-center justify-center p-6 overflow-hidden group-hover:border-fuchsia-400/60">

                            <!-- Animated background effects -->
                            <div class="absolute inset-0 opacity-40 group-hover:opacity-60 transition-opacity duration-500">
                                <div class="absolute top-1/4 right-1/4 w-2 h-2 bg-fuchsia-300 rounded-full animate-pulse"></div>
                                <div class="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse duration-[3000ms]"></div>
                                <div class="absolute top-2/3 right-2/3 w-1 h-1 bg-fuchsia-400 rounded-full animate-pulse duration-[4000ms]"></div>
                                <div class="absolute bottom-1/4 right-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse duration-[2500ms]"></div>
                            </div>

                            <!-- Main icon with enhanced effects -->
                            <i data-lucide="crown" class="w-20 h-20 text-fuchsia-300 opacity-70 group-hover:opacity-100 transition-all duration-500 group-hover:scale-110 drop-shadow-lg mb-3"></i>

                            <!-- VIP tiers indicator -->
                            <div class="text-center">
                                <div class="text-lg font-bold text-fuchsia-200 mb-1">VIP Tiers</div>
                                <div class="flex justify-center space-x-1 mb-2">
                                    <div class="w-2 h-2 bg-fuchsia-400 rounded-full"></div>
                                    <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                                    <div class="w-2 h-2 bg-fuchsia-300 rounded-full"></div>
                                </div>
                                <div class="text-sm text-fuchsia-300/80 font-medium">Bronze • Silver • Gold • Platinum</div>
                            </div>

                            <!-- Corner accent -->
                            <div class="absolute top-3 right-3 flex items-center space-x-1">
                                <div class="w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse"></div>
                                <div class="w-1 h-1 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feature Pillar 3: True Digital Ownership -->
                <div class="feature-pillar-3 feature-card group flex flex-col relative overflow-hidden bg-gradient-to-b from-gray-900/60 to-gray-950/80 border border-cyan-800/30 rounded-3xl p-8 lg:p-10 text-center backdrop-blur-sm hover:bg-gradient-to-b hover:from-cyan-950/40 hover:to-gray-950/90 hover:border-cyan-500/60 transition-all duration-500 ease-out will-change-transform hover:scale-[1.02] hover:-translate-y-2">

                    <!-- Animated background pattern -->
                    <div class="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-cyan-500/20 via-transparent to-blue-500/20 animate-gradient-shift" style="animation-delay: 2s;"></div>
                        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%3E%3Cpath%20d%3D%22M0%200h60v1H0zM0%2060h60v1H0zM0%200v60h1V0zm60%200v60h1V0z%22%20fill%3D%22%2306b6d4%22%20fill-opacity%3D%220.1%22%20%2F%3E%3C%2Fsvg%3E')] animate-pulse duration-[8000ms]"></div>
                    </div>

                    <!-- Enhanced icon container with advanced effects -->
                    <div class="flex justify-center mb-8 relative z-10">
                        <div class="relative">
                            <!-- Multi-layer glow effect -->
                            <div class="absolute -inset-4 bg-cyan-500/30 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                            <div class="absolute -inset-2 bg-cyan-400/20 rounded-full blur-lg opacity-60 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Rotating ring with ownership pattern -->
                            <div class="absolute -inset-3 border-2 border-cyan-500/30 rounded-full animate-spin-slow group-hover:border-cyan-400/60 transition-colors duration-500"></div>

                            <!-- Main icon container -->
                            <div class="relative p-6 bg-gradient-to-br from-cyan-600/50 to-blue-600/50 rounded-full ring-2 ring-cyan-400/50 shadow-2xl group-hover:scale-110 group-hover:ring-cyan-300/70 transition-all duration-500 group-hover:shadow-cyan-500/50">
                                <i data-lucide="wallet" class="w-12 h-12 text-cyan-200 group-hover:text-white transition-colors duration-300 drop-shadow-lg"></i>
                            </div>

                            <!-- Floating ownership particles -->
                            <div class="absolute -inset-6 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                                <div class="absolute top-0 left-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-float-up"></div>
                                <div class="absolute bottom-0 right-1/4 w-1 h-1 bg-blue-400 rounded-full animate-float-up" style="animation-delay: 0.3s;"></div>
                                <div class="absolute top-1/2 left-0 w-1 h-1 bg-cyan-300 rounded-full animate-float-up" style="animation-delay: 0.6s;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced heading with advanced gradient -->
                    <h3 class="text-3xl lg:text-4xl font-black mb-4 relative z-10">
                        <span class="bg-gradient-to-r from-cyan-200 via-blue-200 to-cyan-200 text-transparent bg-clip-text animate-gradient-x">
                            Own Your Digital Wealth
                        </span>
                    </h3>

                    <!-- Enhanced description with better copy -->
                    <p class="text-gray-300 text-base lg:text-lg leading-relaxed mb-8 relative z-10 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                        Take complete control of your rewards.
                        <span class="text-cyan-200 font-bold bg-cyan-500/20 px-2 py-1 rounded-md">$DAN</span> tokens
                        are <span class="text-cyan-300 font-bold">genuine cryptocurrency assets</span> that you truly own.
                        Trade on DEXs, stake for yields, participate in governance, and build your decentralized wealth portfolio.
                    </p>

                    <!-- Enhanced 3D showcase card -->
                    <div class="relative perspective-container mt-auto group/card">
                        <div class="card-3d-effect preserve-3d relative w-full h-48 md:h-56 rounded-2xl bg-gradient-to-br from-cyan-800/60 via-blue-800/50 to-gray-950/70 border-2 border-cyan-500/40 shadow-2xl group-hover:scale-[1.03] group-hover:shadow-cyan-500/30 transition-all duration-500 flex flex-col items-center justify-center p-6 overflow-hidden group-hover:border-cyan-400/60">

                            <!-- Animated background effects -->
                            <div class="absolute inset-0 opacity-40 group-hover:opacity-60 transition-opacity duration-500">
                                <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyan-300 rounded-full animate-pulse"></div>
                                <div class="absolute bottom-1/3 right-1/3 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse duration-[3000ms]"></div>
                                <div class="absolute top-2/3 left-2/3 w-1 h-1 bg-cyan-400 rounded-full animate-pulse duration-[4000ms]"></div>
                                <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-blue-300 rounded-full animate-pulse duration-[2500ms]"></div>
                            </div>

                            <!-- Main icon with enhanced effects -->
                            <i data-lucide="trending-up" class="w-20 h-20 text-cyan-300 opacity-70 group-hover:opacity-100 transition-all duration-500 group-hover:scale-110 drop-shadow-lg mb-3"></i>

                            <!-- Market features indicator -->
                            <div class="text-center">
                                <div class="text-lg font-bold text-cyan-200 mb-1">Market Ready</div>
                                <div class="flex justify-center space-x-2 mb-2">
                                    <span class="text-xs bg-cyan-500/20 text-cyan-300 px-2 py-1 rounded-full">Trade</span>
                                    <span class="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">Stake</span>
                                </div>
                                <div class="text-sm text-cyan-300/80 font-medium">DEX Compatible • DeFi Ready</div>
                            </div>

                            <!-- Corner accent -->
                            <div class="absolute top-3 right-3 flex items-center space-x-1">
                                <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                                <div class="w-1 h-1 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Enhanced bottom call-to-action section -->
            <div class="mt-20 text-center" data-animate="fade-in" data-delay="1600">
                <!-- Compelling headline -->
                <div class="mb-8">
                    <h4 class="text-2xl md:text-3xl font-bold mb-4">
                        <span class="bg-gradient-to-r from-purple-300 via-fuchsia-300 to-cyan-300 text-transparent bg-clip-text animate-gradient-x">
                            Ready to Transform Your Shopping Experience?
                        </span>
                    </h4>
                    <p class="text-gray-400 text-lg max-w-2xl mx-auto">
                        Join thousands of early adopters who are already earning crypto rewards with every purchase
                    </p>
                </div>

                <!-- Enhanced CTA buttons -->
                <div class="flex flex-col sm:flex-row gap-4 items-center justify-center max-w-2xl mx-auto">
                    <!-- Primary CTA -->
                    <div class="relative group">
                        <div class="absolute -inset-2 bg-gradient-to-r from-purple-500/30 via-fuchsia-500/30 to-cyan-500/30 rounded-2xl blur-xl opacity-70 group-hover:opacity-100 transition-opacity duration-500" aria-hidden="true"></div>
                        <a href="#roadmap" class="btn-enhanced relative inline-flex items-center justify-center px-8 py-4 rounded-2xl text-lg font-bold text-white bg-gradient-to-r from-purple-600 via-fuchsia-600 to-cyan-600 hover:from-purple-500 hover:via-fuchsia-500 hover:to-cyan-500 border border-white/20 hover:border-white/30 transition-all duration-500 group shadow-2xl hover:shadow-purple-500/25 will-change-transform hover:scale-105">
                            <i data-lucide="rocket" class="w-5 h-5 mr-3 group-hover:animate-pulse"></i>
                            Explore Our Roadmap
                            <i class="fas fa-arrow-down ml-3 text-sm transition-transform group-hover:translate-y-1 will-change-transform"></i>
                        </a>
                    </div>

                    <!-- Secondary CTA -->
                    <div class="relative group">
                        <div class="absolute -inset-1 bg-gradient-to-r from-gray-500/20 to-gray-400/20 rounded-xl blur-lg opacity-50 group-hover:opacity-80 transition-opacity duration-300" aria-hidden="true"></div>
                        <a href="#qr-code-section" class="btn-enhanced relative inline-flex items-center justify-center px-6 py-3 rounded-xl text-base font-semibold text-gray-200 bg-gray-800/60 hover:bg-gray-700/60 border border-gray-600/50 hover:border-gray-500/60 backdrop-blur-sm transition-all duration-300 group will-change-transform hover:scale-105">
                            <i data-lucide="users" class="w-4 h-4 mr-2 group-hover:text-white transition-colors"></i>
                            Join Whitelist
                            <i class="fas fa-external-link-alt ml-2 text-xs group-hover:scale-110 transition-transform will-change-transform"></i>
                        </a>
                    </div>
                </div>

                <!-- Trust indicators -->
                <div class="mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="shield-check" class="w-4 h-4 text-green-400"></i>
                        <span>Secure & Decentralized</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="zap" class="w-4 h-4 text-yellow-400"></i>
                        <span>Instant Rewards</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="globe" class="w-4 h-4 text-blue-400"></i>
                        <span>Global Community</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Roadmap Section with Advanced Animations -->
    <section id="roadmap" aria-labelledby="roadmap-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-gray-950/90 to-black relative overflow-hidden">
        <!-- Enhanced Background Elements with Advanced Animations -->
        <div class="absolute top-0 left-0 w-2/3 h-2/3 bg-gradient-to-br from-cyan-900/25 to-transparent blur-[120px] -translate-x-1/4 -translate-y-1/4 opacity-70 -z-10 animate-pulse duration-[15000ms]" aria-hidden="true"></div>
        <div class="absolute bottom-0 right-0 w-2/3 h-2/3 bg-gradient-to-tl from-fuchsia-900/25 to-transparent blur-[120px] translate-x-1/4 translate-y-1/4 opacity-70 -z-10 animate-pulse duration-[18000ms] animation-delay-3000" aria-hidden="true"></div>
        <div class="absolute top-1/2 left-1/2 w-1/2 h-1/2 bg-gradient-to-r from-orange-900/20 to-transparent blur-[100px] -translate-x-1/2 -translate-y-1/2 opacity-60 -z-10 animate-pulse duration-[20000ms] animation-delay-5000" aria-hidden="true"></div>

        <!-- Enhanced Grid Pattern with Animation -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10 -z-10 animate-pulse duration-[25000ms]" aria-hidden="true"></div>

        <!-- Enhanced Floating Particles with More Movement -->
        <div class="absolute inset-0 -z-5 opacity-20" aria-hidden="true">
            <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-pulse duration-[8000ms]"></div>
            <div class="absolute top-3/4 left-2/3 w-2 h-2 bg-purple-400 rounded-full animate-pulse duration-[12000ms]"></div>
            <div class="absolute top-1/2 right-1/4 w-1 h-1 bg-fuchsia-400 rounded-full animate-pulse duration-[10000ms]"></div>
            <div class="absolute top-1/6 right-1/3 w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse duration-[14000ms]"></div>
            <div class="absolute bottom-1/4 left-1/6 w-1 h-1 bg-cyan-300 rounded-full animate-pulse duration-[16000ms]"></div>
        </div>

        <!-- Animated Circuit Lines -->
        <div class="absolute inset-0 -z-5 opacity-15" aria-hidden="true">
            <div class="absolute top-1/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent animate-pulse duration-[8000ms]"></div>
            <div class="absolute top-2/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-purple-500/30 to-transparent animate-pulse duration-[10000ms] animation-delay-2000"></div>
            <div class="absolute top-3/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-fuchsia-500/30 to-transparent animate-pulse duration-[12000ms] animation-delay-4000"></div>
        </div>

       <div class="container mx-auto px-4 md:px-6 relative z-10">
            <!-- Enhanced Heading with Advanced Decorative Elements -->
            <div class="text-center mb-20" data-animate="fade-in" data-delay="200">
                <div class="relative inline-block mb-6" data-animate="scale-in" data-delay="400">
                    <!-- Enhanced glow effect behind heading -->
                    <div class="absolute -inset-6 bg-gradient-to-r from-cyan-500/15 via-purple-500/15 via-fuchsia-500/15 to-orange-500/15 rounded-2xl blur-2xl opacity-70 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                    <!-- Orbital rings around title -->
                    <div class="absolute inset-0 w-full h-full">
                        <div class="absolute rounded-full border border-cyan-400/20 animate-spin-slow opacity-60" style="width: calc(100% + 60px); height: calc(100% + 60px); top: -30px; left: -30px; animation-duration: 25s;"></div>
                        <div class="absolute rounded-full border border-purple-400/15 animate-spin-slow-reverse opacity-50" style="width: calc(100% + 100px); height: calc(100% + 100px); top: -50px; left: -50px; animation-duration: 35s;"></div>
                    </div>

                    <h2 id="roadmap-heading" class="relative text-4xl md:text-5xl lg:text-6xl font-bold mb-4 uppercase tracking-wider" data-text-reveal="word-by-word" data-animate="text-reveal" data-delay="600">
                        <span class="bg-gradient-to-r from-cyan-400 via-purple-400 via-fuchsia-400 to-orange-400 text-transparent bg-clip-text animate-gradient-x">
                            Roadmap to Launch
                        </span>
                    </h2>
                </div>

                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto mb-8" data-animate="slide-up" data-delay="800">
                    Charting the course for the Nashop revolution. Each phase brings us closer to transforming decentralized commerce.
                </p>

                <!-- Enhanced Decorative Divider with Animation -->
                <div class="relative flex items-center justify-center" data-animate="scale-in" data-delay="1000">
                    <div class="w-32 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 via-fuchsia-500 to-orange-500 rounded-full animate-pulse duration-[3000ms]"></div>
                    <div class="absolute w-2 h-2 bg-white rounded-full animate-ping opacity-60"></div>
                </div>
            </div>

            <div class="max-w-5xl mx-auto">
                <!-- Enhanced Roadmap Timeline with Advanced Animations -->
                <ol class="relative space-y-28 md:space-y-36 roadmap-line pl-12 roadmap-enhanced">

                    <!-- Phase 1: Enhanced with advanced card styling and effects -->
                    <li class="relative group roadmap-item" data-animate="slide-right" data-delay="1200" style="--glow-color: 56, 189, 248; --phase-color: #38bdf8;"> <!-- Cyan -->
                        <!-- Enhanced milestone marker with advanced pulse effect -->
                        <div class="absolute left-0 top-0 milestone-marker">
                            <div class="absolute inset-0 bg-cyan-400/20 rounded-full animate-ping opacity-75 duration-[2000ms]"></div>
                            <div class="absolute inset-0 bg-cyan-400/10 rounded-full animate-ping opacity-50 duration-[3000ms] animation-delay-500"></div>
                            <div class="relative w-12 h-12 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 ring-[8px] ring-gray-950/80 shadow-2xl flex items-center justify-center transform roadmap-point-glow z-10 group-hover:scale-110 transition-transform duration-500">
                                <i class="fas fa-cogs text-base text-white/90 group-hover:rotate-180 transition-transform duration-700" aria-hidden="true"></i>
                            </div>
                            <!-- Orbital ring around marker -->
                            <div class="absolute inset-0 w-full h-full rounded-full border border-cyan-400/30 animate-spin-slow opacity-0 group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 30px); height: calc(100% + 30px); top: -15px; left: -15px; animation-duration: 20s;"></div>
                        </div>

                        <!-- Enhanced content card with advanced glass morphism -->
                        <div class="ml-20 transform transition-all duration-500 group-hover:translate-x-4 group-hover:scale-[1.02] p-8 rounded-2xl bg-gray-900/50 border border-cyan-800/40 backdrop-blur-md hover:bg-cyan-950/30 hover:border-cyan-600/60 shadow-2xl hover:shadow-cyan-500/30">
                            <!-- Phase label with enhanced styling -->
                            <div class="flex items-center mb-3">
                                <span class="text-sm uppercase tracking-widest text-cyan-400 font-bold mr-3 bg-cyan-500/10 px-3 py-1 rounded-full">Phase 1</span>
                                <div class="h-px flex-grow bg-gradient-to-r from-cyan-500/60 to-transparent"></div>
                                <span class="ml-3 text-sm text-cyan-300/80 font-mono bg-gray-800/50 px-2 py-1 rounded">2023-2025</span>
                            </div>

                            <!-- Enhanced heading with gradient and animation -->
                            <h3 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-cyan-100 to-white bg-clip-text text-transparent mt-2 mb-4 group-hover:scale-105 transition-transform duration-300">Foundation & Architecture</h3>

                            <!-- Content with enhanced styling -->
                            <p class="text-gray-300 opacity-90 leading-relaxed text-base mb-4">
                                Core platform design, smart contract engineering, rigorous security audits, foundational partnerships.
                            </p>

                            <!-- Progress indicators -->
                            <div class="flex items-center gap-2 mb-4">
                                <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-cyan-300">Platform Architecture</span>
                                <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse animation-delay-500"></div>
                                <span class="text-xs text-cyan-300">Smart Contracts</span>
                                <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse animation-delay-1000"></div>
                                <span class="text-xs text-cyan-300">Security Audits</span>
                            </div>

                            <!-- Enhanced decorative element -->
                            <div class="w-16 h-1 bg-gradient-to-r from-cyan-500/80 via-blue-500/60 to-transparent rounded-full mt-4 opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </li>

                    <!-- Phase 2: Enhanced with advanced card styling and effects -->
                    <li class="relative group roadmap-item" data-animate="slide-right" data-delay="1400" style="--glow-color: 168, 85, 247; --phase-color: #a855f7;"> <!-- Purple -->
                        <!-- Enhanced milestone marker with advanced pulse effect -->
                        <div class="absolute left-0 top-0 milestone-marker">
                            <div class="absolute inset-0 bg-purple-400/20 rounded-full animate-ping opacity-75 duration-[2000ms]"></div>
                            <div class="absolute inset-0 bg-purple-400/10 rounded-full animate-ping opacity-50 duration-[3000ms] animation-delay-500"></div>
                            <div class="relative w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 ring-[8px] ring-gray-950/80 shadow-2xl flex items-center justify-center transform roadmap-point-glow z-10 group-hover:scale-110 transition-transform duration-500">
                                <i class="fas fa-coins text-base text-white/90 group-hover:rotate-180 transition-transform duration-700" aria-hidden="true"></i>
                            </div>
                            <!-- Orbital ring around marker -->
                            <div class="absolute inset-0 w-full h-full rounded-full border border-purple-400/30 animate-spin-slow opacity-0 group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 30px); height: calc(100% + 30px); top: -15px; left: -15px; animation-duration: 20s;"></div>
                        </div>

                        <!-- Enhanced content card with advanced glass morphism -->
                        <div class="ml-20 transform transition-all duration-500 group-hover:translate-x-4 group-hover:scale-[1.02] p-8 rounded-2xl bg-gray-900/50 border border-purple-800/40 backdrop-blur-md hover:bg-purple-950/30 hover:border-purple-600/60 shadow-2xl hover:shadow-purple-500/30">
                            <!-- Phase label with enhanced styling -->
                            <div class="flex items-center mb-3">
                                <span class="text-sm uppercase tracking-widest text-purple-400 font-bold mr-3 bg-purple-500/10 px-3 py-1 rounded-full">Phase 2</span>
                                <div class="h-px flex-grow bg-gradient-to-r from-purple-500/60 to-transparent"></div>
                                <span class="ml-3 text-sm text-purple-300/80 font-mono bg-gray-800/50 px-2 py-1 rounded">Q1 2026</span>
                            </div>

                            <!-- Enhanced heading with gradient and animation -->
                            <h3 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent mt-2 mb-4 group-hover:scale-105 transition-transform duration-300">DAN Token Presale</h3>

                            <!-- Content with enhanced styling -->
                            <p class="text-gray-300 opacity-90 leading-relaxed text-base mb-4">
                                Launch of the official $DAN mainnet token presale. Early access investment for our community & supporters.
                            </p>

                            <!-- Progress indicators -->
                            <div class="flex items-center gap-2 mb-4">
                                <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-purple-300">Token Launch</span>
                                <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-500"></div>
                                <span class="text-xs text-purple-300">Presale Campaign</span>
                                <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-1000"></div>
                                <span class="text-xs text-purple-300">Community Access</span>
                            </div>

                            <!-- Enhanced decorative element -->
                            <div class="w-16 h-1 bg-gradient-to-r from-purple-500/80 via-fuchsia-500/60 to-transparent rounded-full mt-4 opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </li>

                    <!-- Phase 3: Enhanced with advanced card styling and effects -->
                    <li class="relative group roadmap-item" data-animate="slide-right" data-delay="1600" style="--glow-color: 217, 70, 239; --phase-color: #d946ef;"> <!-- Fuchsia -->
                        <!-- Enhanced milestone marker with advanced pulse effect -->
                        <div class="absolute left-0 top-0 milestone-marker">
                            <div class="absolute inset-0 bg-fuchsia-400/20 rounded-full animate-ping opacity-75 duration-[2000ms]"></div>
                            <div class="absolute inset-0 bg-fuchsia-400/10 rounded-full animate-ping opacity-50 duration-[3000ms] animation-delay-500"></div>
                            <div class="relative w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-fuchsia-600 ring-[8px] ring-gray-950/80 shadow-2xl flex items-center justify-center transform roadmap-point-glow z-10 group-hover:scale-110 transition-transform duration-500">
                                <i class="fas fa-flask-vial text-base text-white/90 group-hover:rotate-180 transition-transform duration-700" aria-hidden="true"></i>
                            </div>
                            <!-- Orbital ring around marker -->
                            <div class="absolute inset-0 w-full h-full rounded-full border border-fuchsia-400/30 animate-spin-slow opacity-0 group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 30px); height: calc(100% + 30px); top: -15px; left: -15px; animation-duration: 20s;"></div>
                        </div>

                        <!-- Enhanced content card with advanced glass morphism -->
                        <div class="ml-20 transform transition-all duration-500 group-hover:translate-x-4 group-hover:scale-[1.02] p-8 rounded-2xl bg-gray-900/50 border border-fuchsia-800/40 backdrop-blur-md hover:bg-fuchsia-950/30 hover:border-fuchsia-600/60 shadow-2xl hover:shadow-fuchsia-500/30">
                            <!-- Phase label with enhanced styling -->
                            <div class="flex items-center mb-3">
                                <span class="text-sm uppercase tracking-widest text-fuchsia-400 font-bold mr-3 bg-fuchsia-500/10 px-3 py-1 rounded-full">Phase 3</span>
                                <div class="h-px flex-grow bg-gradient-to-r from-fuchsia-500/60 to-transparent"></div>
                                <span class="ml-3 text-sm text-fuchsia-300/80 font-mono bg-gray-800/50 px-2 py-1 rounded">Q2 2026</span>
                            </div>

                            <!-- Enhanced heading with gradient and animation -->
                            <h3 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-fuchsia-100 to-white bg-clip-text text-transparent mt-2 mb-4 group-hover:scale-105 transition-transform duration-300">Platform Beta Access</h3>

                            <!-- Content with enhanced styling -->
                            <p class="text-gray-300 opacity-90 leading-relaxed text-base mb-4">
                                Invite-only beta program launch. Testing core functionality, gathering user feedback, iterative improvements.
                            </p>

                            <!-- Progress indicators -->
                            <div class="flex items-center gap-2 mb-4">
                                <div class="w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-fuchsia-300">Beta Testing</span>
                                <div class="w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse animation-delay-500"></div>
                                <span class="text-xs text-fuchsia-300">User Feedback</span>
                                <div class="w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse animation-delay-1000"></div>
                                <span class="text-xs text-fuchsia-300">Platform Optimization</span>
                            </div>

                            <!-- Enhanced decorative element -->
                            <div class="w-16 h-1 bg-gradient-to-r from-fuchsia-500/80 via-pink-500/60 to-transparent rounded-full mt-4 opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                    </li>

                    <!-- Phase 4: Enhanced with advanced card styling and effects -->
                    <li class="relative group roadmap-item" data-animate="slide-right" data-delay="1800" style="--glow-color: 249, 115, 22; --phase-color: #f97316;"> <!-- Orange -->
                        <!-- Enhanced milestone marker with advanced pulse effect -->
                        <div class="absolute left-0 top-0 milestone-marker">
                            <div class="absolute inset-0 bg-orange-400/20 rounded-full animate-ping opacity-75 duration-[2000ms]"></div>
                            <div class="absolute inset-0 bg-orange-400/10 rounded-full animate-ping opacity-50 duration-[3000ms] animation-delay-500"></div>
                            <div class="relative w-12 h-12 rounded-full bg-gradient-to-br from-fuchsia-500 to-orange-500 ring-[8px] ring-gray-950/80 shadow-2xl flex items-center justify-center transform roadmap-point-glow z-10 group-hover:scale-110 transition-transform duration-500">
                                <i class="fas fa-rocket text-base text-white/90 group-hover:rotate-12 group-hover:scale-110 transition-transform duration-700" aria-hidden="true"></i>
                            </div>
                            <!-- Orbital ring around marker -->
                            <div class="absolute inset-0 w-full h-full rounded-full border border-orange-400/30 animate-spin-slow opacity-0 group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 30px); height: calc(100% + 30px); top: -15px; left: -15px; animation-duration: 20s;"></div>
                            <!-- Special launch effect for final phase -->
                            <div class="absolute inset-0 w-full h-full rounded-full border border-yellow-400/20 animate-spin-slow-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 50px); height: calc(100% + 50px); top: -25px; left: -25px; animation-duration: 30s;"></div>
                        </div>

                        <!-- Enhanced content card with advanced glass morphism and special launch styling -->
                        <div class="ml-20 transform transition-all duration-500 group-hover:translate-x-4 group-hover:scale-[1.02] p-8 rounded-2xl bg-gray-900/50 border border-orange-800/40 backdrop-blur-md hover:bg-orange-950/30 hover:border-orange-600/60 shadow-2xl hover:shadow-orange-500/30 relative overflow-hidden">
                            <!-- Special launch background effect -->
                            <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-yellow-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Phase label with enhanced styling -->
                            <div class="flex items-center mb-3 relative z-10">
                                <span class="text-sm uppercase tracking-widest text-orange-400 font-bold mr-3 bg-orange-500/10 px-3 py-1 rounded-full border border-orange-500/20">Phase 4</span>
                                <div class="h-px flex-grow bg-gradient-to-r from-orange-500/60 to-transparent"></div>
                                <span class="ml-3 text-sm text-orange-300/80 font-mono bg-gray-800/50 px-2 py-1 rounded">Q4 2026</span>
                            </div>

                            <!-- Enhanced heading with gradient and animation -->
                            <h3 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white via-orange-100 to-white bg-clip-text text-transparent mt-2 mb-4 group-hover:scale-105 transition-transform duration-300 relative z-10">Global Mainnet Launch</h3>

                            <!-- Content with enhanced styling -->
                            <p class="text-gray-300 opacity-90 leading-relaxed text-base mb-4 relative z-10">
                                Full public release of the Nashop.store platform, activating the $DAN token economy globally.
                            </p>

                            <!-- Progress indicators -->
                            <div class="flex items-center gap-2 mb-4 relative z-10">
                                <div class="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-orange-300">Global Launch</span>
                                <div class="w-2 h-2 bg-orange-400 rounded-full animate-pulse animation-delay-500"></div>
                                <span class="text-xs text-orange-300">Token Economy</span>
                                <div class="w-2 h-2 bg-orange-400 rounded-full animate-pulse animation-delay-1000"></div>
                                <span class="text-xs text-orange-300">Worldwide Access</span>
                            </div>

                            <!-- Enhanced decorative element with special launch styling -->
                            <div class="w-20 h-1 bg-gradient-to-r from-orange-500/80 via-yellow-500/60 to-transparent rounded-full mt-4 opacity-70 group-hover:opacity-100 transition-opacity duration-300 relative z-10"></div>

                            <!-- Launch celebration particles -->
                            <div class="absolute top-2 right-2 w-1 h-1 bg-orange-400 rounded-full animate-ping opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                            <div class="absolute bottom-4 left-4 w-1 h-1 bg-yellow-400 rounded-full animate-ping opacity-0 group-hover:opacity-70 transition-opacity duration-500 animation-delay-300"></div>
                        </div>
                    </li>
                </ol>

                <!-- Enhanced decorative bottom element with advanced animations -->
                <div class="mt-20 text-center animate-fade-in-up" style="animation-delay: 0.5s;" data-animate="fade-in" data-delay="2000">
                    <div class="relative inline-block">
                        <!-- Enhanced background glow -->
                        <div class="absolute -inset-4 bg-gradient-to-r from-cyan-500/15 via-purple-500/15 via-fuchsia-500/15 to-orange-500/15 rounded-2xl blur-2xl opacity-70 animate-pulse duration-[4000ms]" aria-hidden="true"></div>

                        <!-- Orbital rings -->
                        <div class="absolute inset-0 w-full h-full">
                            <div class="absolute rounded-full border border-cyan-400/20 animate-spin-slow opacity-60" style="width: calc(100% + 40px); height: calc(100% + 40px); top: -20px; left: -20px; animation-duration: 20s;"></div>
                            <div class="absolute rounded-full border border-orange-400/15 animate-spin-slow-reverse opacity-50" style="width: calc(100% + 60px); height: calc(100% + 60px); top: -30px; left: -30px; animation-duration: 30s;"></div>
                        </div>

                        <div class="relative inline-flex items-center justify-center px-8 py-4 rounded-2xl text-lg font-bold text-white bg-gradient-to-r from-cyan-600/40 via-purple-600/40 via-fuchsia-600/40 to-orange-600/40 border border-white/20 hover:border-white/30 transition-all duration-500 transform hover:scale-105 shadow-2xl backdrop-blur-sm">
                            <i class="fas fa-infinity mr-3 text-cyan-300 animate-pulse"></i>
                            <span class="bg-gradient-to-r from-cyan-300 via-purple-300 via-fuchsia-300 to-orange-300 text-transparent bg-clip-text animate-gradient-x">The Journey Continues Beyond</span>
                            <i class="fas fa-rocket ml-3 text-orange-300 animate-pulse animation-delay-500"></i>
                        </div>

                        <!-- Floating particles around the element -->
                        <div class="absolute top-0 left-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-ping opacity-60"></div>
                        <div class="absolute bottom-0 right-1/4 w-1 h-1 bg-orange-400 rounded-full animate-ping opacity-60 animation-delay-1000"></div>
                        <div class="absolute top-1/2 left-0 w-1 h-1 bg-purple-400 rounded-full animate-ping opacity-60 animation-delay-500"></div>
                        <div class="absolute top-1/2 right-0 w-1 h-1 bg-fuchsia-400 rounded-full animate-ping opacity-60 animation-delay-1500"></div>
                    </div>

                    <!-- Additional inspirational text -->
                    <p class="mt-6 text-gray-400 text-base max-w-2xl mx-auto opacity-80" data-animate="slide-up" data-delay="2200">
                        Each milestone brings us closer to revolutionizing decentralized commerce.
                        <span class="text-cyan-300 font-semibold">Join us</span> on this extraordinary journey.
                    </p>
                </div>
            </div>
       </div>
    </section>

    <!-- Partners Section -->
    <section id="partners" aria-labelledby="partners-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-blue-950/15 to-black relative overflow-hidden">
        <!-- Background Glows -->
        <div class="absolute top-0 left-0 w-1/2 h-1/2 bg-gradient-to-br from-blue-900/20 to-transparent blur-[90px] -translate-x-1/4 -translate-y-1/4 opacity-60 -z-10" aria-hidden="true"></div>
        <div class="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-to-tl from-purple-900/20 to-transparent blur-[90px] translate-x-1/4 translate-y-1/4 opacity-60 -z-10" aria-hidden="true"></div>

        <!-- Container for Centering Text -->
        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <h2 id="partners-heading" class="text-4xl md:text-5xl lg:text-6xl font-bold mb-5 uppercase tracking-tighter" data-text-reveal="letter-by-letter" data-animate="text-reveal" data-delay="400">
                    <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 text-transparent bg-clip-text animate-gradient-x">
                        Our Ecosystem Partners
                    </span>
                </h2>
                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto" data-animate="slide-up" data-delay="800">
                    Proudly collaborating with pioneers in the Web3 space.
                </p>
            </div>
        </div>

        <!-- Carousel Container -->
        <div class="relative max-w-5xl mx-auto px-4 md:px-6 overflow-hidden">
            <div class="absolute left-0 top-0 bottom-0 w-24 md:w-32 bg-gradient-to-r from-black to-transparent z-10 pointer-events-none"></div>
            <div class="absolute right-0 top-0 bottom-0 w-24 md:w-32 bg-gradient-to-l from-black to-transparent z-10 pointer-events-none"></div>

            <div class="carouselTrack animate-marquee" id="partner-marquee">
                <!-- Logo Sets Populated by JS -->
                <div id="partner-logos-set1" class="carouselTrackContent flex items-center h-24" role="list">
                    <!-- JS populates .brandLogo divs here -->
                </div>
                <div id="partner-logos-set2" aria-hidden="true" class="carouselTrackContent flex items-center h-24">
                    <!-- JS populates .brandLogo divs here -->
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section id="team" aria-labelledby="team-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-purple-950/15 to-black relative overflow-hidden">
        <!-- Background Glows -->
        <div class="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-purple-900/20 to-transparent blur-[90px] -translate-x-1/4 -translate-y-1/4 opacity-60 -z-10" aria-hidden="true"></div>
        <div class="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-fuchsia-900/20 to-transparent blur-[90px] translate-x-1/4 translate-y-1/4 opacity-60 -z-10" aria-hidden="true"></div>

        <!-- Subtle Grid Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10 -z-10" aria-hidden="true"></div>

        <!-- Floating Particles -->
        <div class="absolute inset-0 -z-5 opacity-20" aria-hidden="true">
            <div class="absolute top-1/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-pulse duration-[8000ms]"></div>
            <div class="absolute top-3/4 right-2/3 w-2 h-2 bg-fuchsia-400 rounded-full animate-pulse duration-[12000ms]"></div>
            <div class="absolute top-1/2 left-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-pulse duration-[10000ms]"></div>
        </div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <!-- Section Heading -->
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <div class="relative inline-block mb-4" data-animate="scale-in" data-delay="400">
                    <!-- Glow effect behind heading -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-purple-500/10 via-fuchsia-500/10 to-cyan-500/10 rounded-lg blur-xl opacity-70 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                    <h2 id="team-heading" class="relative text-4xl md:text-5xl lg:text-6xl font-bold mb-4 uppercase tracking-tighter" data-text-reveal="word-by-word" data-animate="text-reveal" data-delay="600">
                        <span class="bg-gradient-to-r from-purple-400 via-fuchsia-400 to-cyan-400 text-transparent bg-clip-text animate-gradient-x">
                            Our Team
                        </span>
                    </h2>
                </div>

                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto" data-animate="slide-up" data-delay="800">
                    Meet the visionaries building the future of decentralized commerce.
                </p>

                <!-- Decorative Divider -->
                <div class="w-24 h-1 bg-gradient-to-r from-purple-500 via-fuchsia-500 to-cyan-500 mx-auto mt-6 rounded-full" data-animate="scale-in" data-delay="1000"></div>
            </div>

            <!-- Enhanced Team Profiles Display -->
            <div class="flex flex-col items-center justify-center w-full max-w-5xl mx-auto animate-fade-in-up" style="animation-delay: 0.2s;">
                <!-- Decorative cosmic elements -->
                <div class="absolute left-1/4 top-1/3 w-24 h-24 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full blur-[50px] animate-pulse duration-[8000ms] -z-1" aria-hidden="true"></div>
                <div class="absolute right-1/4 bottom-1/3 w-32 h-32 bg-gradient-to-tl from-cyan-500/10 to-transparent rounded-full blur-[60px] animate-pulse duration-[10000ms] -z-1" aria-hidden="true"></div>

                <!-- Team members container with cosmic connection - improved for mobile -->
                <div class="relative flex flex-row items-center justify-center gap-4 sm:gap-6 md:gap-12 lg:gap-16 mt-8 mb-24 sm:mb-20 perspective-1000">
                    <!-- Enhanced cosmic connection line - visible on all screen sizes -->
                    <div class="absolute top-1/2 left-1/2 h-1 w-[95%] -translate-x-1/2 -translate-y-1/2 z-0">
                        <div class="h-full w-full bg-gradient-to-r from-purple-600/20 via-fuchsia-600/30 via-cyan-600/30 to-orange-600/20 rounded-full blur-[2px]"></div>
                        <!-- Enhanced animated particles on the line -->
                        <div class="absolute top-1/2 left-0 w-2 h-2 -translate-y-1/2 bg-purple-400/70 rounded-full blur-[1px] animate-pulse duration-[4000ms]"></div>
                        <div class="absolute top-1/2 left-1/4 w-1.5 h-1.5 -translate-y-1/2 bg-fuchsia-400/70 rounded-full blur-[1px] animate-pulse duration-[5000ms]"></div>
                        <div class="absolute top-1/2 left-1/2 w-2 h-2 -translate-y-1/2 bg-cyan-400/70 rounded-full blur-[1px] animate-pulse duration-[6000ms]"></div>
                        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 -translate-y-1/2 bg-orange-400/70 rounded-full blur-[1px] animate-pulse duration-[7000ms]"></div>
                    </div>

                   

                    <!-- Profile Card 1: Enhanced with role and mobile-friendly sizing -->
                    <div class="avatar-container group relative">
                        <!-- Orbital ring animation - visible on hover and touch -->
                        <div class="absolute inset-0 w-full h-full rounded-full border border-purple-500/30 animate-spin-slow opacity-0 group-hover:opacity-100 sm:group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 20px); height: calc(100% + 20px); top: -10px; left: -10px;"></div>
                        <div class="absolute inset-0 w-full h-full rounded-full border border-fuchsia-500/20 animate-spin-slow-reverse opacity-0 group-hover:opacity-100 sm:group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 40px); height: calc(100% + 40px); top: -20px; left: -20px;"></div>

                        <!-- Card with enhanced 3D effect - responsive sizing -->
                        <div class="avatar group relative w-[150px] sm:w-[180px] md:w-[220px] lg:w-[250px] aspect-square cursor-pointer transition-all duration-500 ease-out transform-gpu hover:scale-105 z-10">
                            <div class="relative w-full h-full transition-all duration-500 ease-out group-hover:rotate-x-4 group-hover:-rotate-y-8 shadow-2xl shadow-purple-900/20">

                                <!-- Enhanced background glow -->
                                <div class="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-fuchsia-900/20 to-transparent rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>

                                <!-- SVG Avatar Container with custom clip path - scaled for all devices -->
                                <div class="avatar-img h-full w-full bg-transparent overflow-hidden" style="clip-path: path('M 0 -50 L 250 -50 L 250 180 C 125 180 0 300 0 180 Z'); -webkit-clip-path: path('M 0 -50 L 250 -50 L 250 180 C 125 180 0 300 0 180 Z');">
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-900/20 to-fuchsia-900/10 transition-all duration-500 ease-out transform-origin-bottom scale-90 group-hover:scale-100">
                                        <svg width="100%" height="100%" viewBox="0 0 250 250" class="transition-all duration-500 group-hover:scale-110">
                                            <!-- Embedded Founder SVG -->
                                            <defs>
                                                <linearGradient id="founderSkinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
                                                    <stop offset="50%" style="stop-color:#9333ea;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
                                                </linearGradient>
                                                <linearGradient id="founderHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#4c1d95;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#581c87;stop-opacity:1" />
                                                </linearGradient>
                                                <linearGradient id="founderClothingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
                                                    <stop offset="50%" style="stop-color:#374151;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#4b5563;stop-opacity:1" />
                                                </linearGradient>
                                                <radialGradient id="founderGlow" cx="50%" cy="50%" r="50%">
                                                    <stop offset="0%" style="stop-color:#a855f7;stop-opacity:0.3" />
                                                    <stop offset="100%" style="stop-color:#9333ea;stop-opacity:0" />
                                                </radialGradient>
                                            </defs>

                                            <!-- Background glow -->
                                            <circle cx="125" cy="125" r="120" fill="url(#founderGlow)" opacity="0.6">
                                                <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Orbital rings -->
                                            <circle cx="125" cy="125" r="105" fill="none" stroke="#a855f7" stroke-width="1" opacity="0.4">
                                                <animateTransform attributeName="transform" type="rotate" values="360 125 125;0 125 125" dur="18s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Body/Hoodie -->
                                            <ellipse cx="125" cy="200" rx="50" ry="40" fill="url(#founderClothingGradient)"/>

                                            <!-- Hoodie details -->
                                            <path d="M 85 180 Q 125 160 165 180 L 160 200 Q 125 185 90 200 Z" fill="url(#founderClothingGradient)" opacity="0.8"/>

                                            <!-- Neck -->
                                            <rect x="115" y="165" width="20" height="25" fill="url(#founderSkinGradient)" rx="10"/>

                                            <!-- Head -->
                                            <circle cx="125" cy="140" r="35" fill="url(#founderSkinGradient)"/>

                                            <!-- Hair (more modern/tech style) -->
                                            <path d="M 95 125 Q 125 100 155 125 Q 150 115 125 110 Q 100 115 95 125" fill="url(#founderHairGradient)"/>
                                            <path d="M 110 120 Q 125 115 140 120" fill="url(#founderHairGradient)" opacity="0.7"/>

                                            <!-- Eyes (with tech glow) -->
                                            <circle cx="115" cy="135" r="3" fill="#1f2937"/>
                                            <circle cx="135" cy="135" r="3" fill="#1f2937"/>
                                            <circle cx="116" cy="134" r="1" fill="#a855f7">
                                                <animate attributeName="fill" values="#a855f7;#ffffff;#a855f7" dur="2s" repeatCount="indefinite"/>
                                            </circle>
                                            <circle cx="136" cy="134" r="1" fill="#a855f7">
                                                <animate attributeName="fill" values="#a855f7;#ffffff;#a855f7" dur="2s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Nose -->
                                            <ellipse cx="125" cy="145" rx="2" ry="4" fill="#7c3aed" opacity="0.6"/>

                                            <!-- Mouth -->
                                            <path d="M 120 155 Q 125 160 130 155" stroke="#1f2937" stroke-width="2" fill="none" stroke-linecap="round"/>

                                            <!-- Tech/Code symbols -->
                                            <text x="105" y="190" font-family="monospace" font-size="8" fill="#a855f7" opacity="0.8">&lt;/&gt;</text>

                                            <!-- Developer badge -->
                                            <rect x="140" y="175" width="15" height="8" fill="#a855f7" rx="2" opacity="0.8"/>
                                            <text x="142" y="181" font-family="monospace" font-size="4" fill="#ffffff">DEV</text>

                                            <!-- Floating code particles -->
                                            <text x="75" y="110" font-family="monospace" font-size="6" fill="#a855f7" opacity="0.6">{}</text>
                                            <text x="165" y="130" font-family="monospace" font-size="5" fill="#9333ea" opacity="0.7">[]</text>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Enhanced shine effect -->
                                <div class="absolute inset-0 overflow-hidden z-0">
                                    <div class="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-gradient-to-r from-white/15 via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-in-out transform group-hover:rotate-12 group-hover:scale-110"></div>
                                </div>

                                <!-- Enhanced profile info with role - improved for mobile -->
                                <div class="absolute -bottom-16 sm:-bottom-20 left-1/2 transform -translate-x-1/2 w-[95%] flex flex-col items-center gap-1 z-10">
                                    <!-- Username with enhanced styling - responsive text size -->
                                    <div class="flex items-center justify-center gap-1 sm:gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-xl bg-gray-900/80 backdrop-blur-md border border-purple-500/30 shadow-lg shadow-purple-900/30 transition-all duration-300 group-hover:shadow-purple-500/40">
                                        <span class="bg-gradient-to-r from-purple-300 via-fuchsia-400 to-cyan-400 bg-clip-text text-transparent font-bold text-sm sm:text-base md:text-lg">@black_dann</span>
                                        <!-- Pulsing Online Icon -->
                                        <i class="fas fa-circle text-green-500 text-[0.5rem] sm:text-[0.6rem] animate-pulse"></i>
                                    </div>
                                    <!-- Role with subtle animation - always visible on mobile -->
                                    <div class="px-2 sm:px-3 py-1 rounded-lg bg-black/40 backdrop-blur-sm text-xs sm:text-sm font-medium text-gray-300 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-all duration-500 delay-200 transform sm:translate-y-2 sm:group-hover:translate-y-0">
                                        <span class="text-purple-300">Founder & Lead Developer</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Card 2: Enhanced with role and mobile-friendly sizing -->
                    <div class="avatar-container group relative">
                        <!-- Orbital ring animation - visible on hover and touch -->
                        <div class="absolute inset-0 w-full h-full rounded-full border border-cyan-500/30 animate-spin-slow opacity-0 group-hover:opacity-100 sm:group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 20px); height: calc(100% + 20px); top: -10px; left: -10px;"></div>
                        <div class="absolute inset-0 w-full h-full rounded-full border border-fuchsia-500/20 animate-spin-slow-reverse opacity-0 group-hover:opacity-100 sm:group-hover:opacity-100 transition-opacity duration-500" style="width: calc(100% + 40px); height: calc(100% + 40px); top: -20px; left: -20px;"></div>

                        <!-- Card with enhanced 3D effect - responsive sizing -->
                        <div class="avatar group relative w-[150px] sm:w-[180px] md:w-[220px] lg:w-[250px] aspect-square cursor-pointer transition-all duration-500 ease-out transform-gpu hover:scale-105 z-10">
                            <div class="relative w-full h-full transition-all duration-500 ease-out group-hover:rotate-x-4 group-hover:rotate-y-8 shadow-2xl shadow-cyan-900/20">

                                <!-- Enhanced background glow -->
                                <div class="absolute inset-0 bg-gradient-to-bl from-cyan-900/30 via-fuchsia-900/20 to-transparent rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>

                                <!-- SVG Avatar Container with custom clip path - scaled for all devices -->
                                <div class="avatar-img h-full w-full bg-transparent overflow-hidden" style="clip-path: path('M 0 -50 L 250 -50 L 250 180 C 125 180 0 300 0 180 Z'); -webkit-clip-path: path('M 0 -50 L 250 -50 L 250 180 C 125 180 0 300 0 180 Z');">
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-cyan-900/20 to-fuchsia-900/10 transition-all duration-500 ease-out transform-origin-bottom scale-90 group-hover:scale-100">
                                        <svg width="100%" height="100%" viewBox="0 0 250 250" class="transition-all duration-500 group-hover:scale-110">
                                            <!-- Embedded Creative Director SVG -->
                                            <defs>
                                                <linearGradient id="creativeSkinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                                    <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
                                                </linearGradient>
                                                <linearGradient id="creativeHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#164e63;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#0c4a6e;stop-opacity:1" />
                                                </linearGradient>
                                                <linearGradient id="creativeClothingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
                                                    <stop offset="50%" style="stop-color:#db2777;stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
                                                </linearGradient>
                                                <radialGradient id="creativeGlow" cx="50%" cy="50%" r="50%">
                                                    <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.3" />
                                                    <stop offset="100%" style="stop-color:#0891b2;stop-opacity:0" />
                                                </radialGradient>
                                            </defs>

                                            <!-- Background glow -->
                                            <circle cx="125" cy="125" r="120" fill="url(#creativeGlow)" opacity="0.6">
                                                <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Orbital rings -->
                                            <circle cx="125" cy="125" r="108" fill="none" stroke="#06b6d4" stroke-width="1" opacity="0.3">
                                                <animateTransform attributeName="transform" type="rotate" values="0 125 125;360 125 125" dur="22s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Body/Creative Shirt -->
                                            <ellipse cx="125" cy="200" rx="48" ry="38" fill="url(#creativeClothingGradient)"/>

                                            <!-- Creative pattern on shirt -->
                                            <circle cx="115" cy="190" r="3" fill="#06b6d4" opacity="0.7">
                                                <animate attributeName="r" values="2;4;2" dur="3s" repeatCount="indefinite"/>
                                            </circle>
                                            <circle cx="135" cy="195" r="2" fill="#ec4899" opacity="0.8">
                                                <animate attributeName="r" values="1;3;1" dur="2.5s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Neck -->
                                            <rect x="115" y="165" width="20" height="25" fill="url(#creativeSkinGradient)" rx="10"/>

                                            <!-- Head -->
                                            <circle cx="125" cy="140" r="35" fill="url(#creativeSkinGradient)"/>

                                            <!-- Hair (creative/artistic style) -->
                                            <path d="M 92 128 Q 125 98 158 128 Q 152 118 125 108 Q 98 118 92 128" fill="url(#creativeHairGradient)"/>
                                            <path d="M 105 125 Q 125 120 145 125" fill="url(#creativeHairGradient)" opacity="0.8"/>

                                            <!-- Eyes (creative spark) -->
                                            <circle cx="115" cy="135" r="3" fill="#1f2937"/>
                                            <circle cx="135" cy="135" r="3" fill="#1f2937"/>
                                            <circle cx="116" cy="134" r="1" fill="#06b6d4">
                                                <animate attributeName="fill" values="#06b6d4;#ec4899;#06b6d4" dur="3s" repeatCount="indefinite"/>
                                            </circle>
                                            <circle cx="136" cy="134" r="1" fill="#06b6d4">
                                                <animate attributeName="fill" values="#06b6d4;#ec4899;#06b6d4" dur="3s" repeatCount="indefinite"/>
                                            </circle>

                                            <!-- Nose -->
                                            <ellipse cx="125" cy="145" rx="2" ry="4" fill="#0e7490" opacity="0.6"/>

                                            <!-- Mouth -->
                                            <path d="M 120 155 Q 125 160 130 155" stroke="#1f2937" stroke-width="2" fill="none" stroke-linecap="round"/>

                                            <!-- Creative tools/symbols -->
                                            <path d="M 145 175 L 155 185 M 155 175 L 145 185" stroke="#06b6d4" stroke-width="2" opacity="0.8"/>
                                            <circle cx="150" cy="180" r="8" fill="none" stroke="#ec4899" stroke-width="1" opacity="0.7"/>

                                            <!-- Design elements -->
                                            <polygon points="95,185 100,175 105,185" fill="#06b6d4" opacity="0.7">
                                                <animateTransform attributeName="transform" type="rotate" values="0 100 180;360 100 180" dur="8s" repeatCount="indefinite"/>
                                            </polygon>

                                            <!-- Floating creative particles -->
                                            <circle cx="80" cy="115" r="2" fill="#ec4899" opacity="0.8">
                                                <animateTransform attributeName="transform" type="translate" values="0,0;20,-20;0,0" dur="5s" repeatCount="indefinite"/>
                                            </circle>
                                            <rect x="165" y="125" width="4" height="4" fill="#06b6d4" opacity="0.6" transform="rotate(45 167 127)">
                                                <animateTransform attributeName="transform" type="translate" values="0,0;-15,15;0,0" dur="6s" repeatCount="indefinite"/>
                                            </rect>

                                            <!-- Creativity badge -->
                                            <circle cx="110" cy="175" r="6" fill="#ec4899" opacity="0.8"/>
                                            <path d="M 107 175 L 109 177 L 113 173" stroke="#ffffff" stroke-width="1" fill="none"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Enhanced shine effect -->
                                <div class="absolute inset-0 overflow-hidden z-0">
                                    <div class="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-gradient-to-r from-white/15 via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-in-out transform group-hover:rotate-12 group-hover:scale-110"></div>
                                </div>

                                <!-- Enhanced profile info with role - improved for mobile -->
                                <div class="absolute -bottom-16 sm:-bottom-20 left-1/2 transform -translate-x-1/2 w-[95%] flex flex-col items-center gap-1 z-10">
                                    <!-- Username with enhanced styling - responsive text size -->
                                    <div class="flex items-center justify-center gap-1 sm:gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-xl bg-gray-900/80 backdrop-blur-md border border-cyan-500/30 shadow-lg shadow-cyan-900/30 transition-all duration-300 group-hover:shadow-cyan-500/40">
                                        <span class="bg-gradient-to-r from-cyan-300 via-fuchsia-400 to-purple-400 bg-clip-text text-transparent font-bold text-sm sm:text-base md:text-lg">@SUXO_PLATOR</span>
                                        <!-- Pulsing Online Icon -->
                                        <i class="fas fa-circle text-green-500 text-[0.5rem] sm:text-[0.6rem] animate-pulse"></i>
                                    </div>
                                    <!-- Role with subtle animation - always visible on mobile -->
                                    <div class="px-2 sm:px-3 py-1 rounded-lg bg-black/40 backdrop-blur-sm text-xs sm:text-sm font-medium text-gray-300 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-all duration-500 delay-200 transform sm:translate-y-2 sm:group-hover:translate-y-0">
                                        <span class="text-cyan-300">Creative Director & Tokenomics</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Decorative Bottom Elements -->
            <div class="mt-16 flex flex-col sm:flex-row items-center justify-center gap-4 animate-fade-in-up" style="animation-delay: 0.4s;">
                <!-- Community Link -->
                <div class="inline-block relative">
                    <div class="absolute -inset-1 bg-gradient-to-r from-purple-500/10 via-fuchsia-500/10 to-cyan-500/10 rounded-lg blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>
                    <a href="#join" class="relative inline-flex items-center justify-center px-6 py-2 rounded-full text-sm font-medium text-white bg-gradient-to-r from-purple-600/30 to-cyan-600/30 border border-white/10 hover:border-white/20 transition-all duration-300 group">
                        Join Our Community
                        <i class="fas fa-arrow-down ml-2 text-xs transition-transform group-hover:translate-y-0.5"></i>
                    </a>
                </div>

                <!-- Mobile App Link -->
                <div class="inline-block relative">
                    <div class="absolute -inset-1 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-fuchsia-500/10 rounded-lg blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>
                    <a href="#mobile-app" class="relative inline-flex items-center justify-center px-6 py-2 rounded-full text-sm font-medium text-white bg-gradient-to-r from-blue-600/30 to-purple-600/30 border border-white/10 hover:border-white/20 transition-all duration-300 group">
                        Explore Mobile App
                        <i class="fas fa-mobile-alt ml-2 text-xs transition-transform group-hover:translate-y-0.5"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile App Section -->
    <section id="mobile-app" aria-labelledby="mobile-app-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-blue-950/10 to-black relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 -z-10 opacity-20" aria-hidden="true">
            <div class="absolute top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-blue-600/20 to-transparent rounded-full blur-[120px] opacity-60 translate-x-1/4 -translate-y-1/4 animate-pulse duration-[15000ms]"></div>
            <div class="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-purple-600/20 to-transparent rounded-full blur-[120px] opacity-60 -translate-x-1/4 translate-y-1/4 animate-pulse duration-[18000ms] animation-delay-3000"></div>
        </div>

        <!-- Subtle Grid Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10 -z-10" aria-hidden="true"></div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <div class="relative inline-block mb-4" data-animate="scale-in" data-delay="400">
                    <!-- Glow effect behind heading -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-fuchsia-500/10 rounded-lg blur-xl opacity-70 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                    <h2 id="mobile-app-heading" class="relative text-4xl md:text-5xl lg:text-6xl font-bold mb-4 uppercase tracking-tighter" data-text-reveal="letter-by-letter" data-animate="text-reveal" data-delay="600">
                        <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-fuchsia-400 text-transparent bg-clip-text animate-gradient-x">
                            Mobile Experience
                        </span>
                    </h2>
                </div>

                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto" data-animate="slide-up" data-delay="800">
                    Shop, earn, and manage your $DAN tokens on the go with our powerful mobile applications.
                </p>

                <!-- Decorative Divider -->
                <div class="w-24 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-fuchsia-500 mx-auto mt-6 rounded-full" data-animate="scale-in" data-delay="1000"></div>
            </div>

            <!-- Enhanced Mobile App Content -->
            <div class="flex flex-col lg:flex-row items-center justify-between gap-12 lg:gap-6">
                <!-- Left Side: Enhanced Interactive Phone Interface -->
                <div class="w-full lg:w-1/2 relative perspective-1000" data-animate="slide-left" data-delay="400">
                    <!-- Enhanced Interactive Phone Container -->
                    <div class="phone-interface phone-interface-container relative mx-auto w-[320px] md:w-[380px] h-[600px] md:h-[700px] transform-gpu cursor-pointer group will-change-transform">

                        <!-- Cosmic Background Glow -->
                        <div class="absolute -inset-8 bg-gradient-to-br from-purple-600/30 via-fuchsia-600/20 to-cyan-600/30 rounded-full blur-3xl opacity-60 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>

                        <!-- Main Phone Frame -->
                        <div class="relative w-full h-full rounded-[50px] bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border-4 border-gray-700 shadow-2xl overflow-hidden group-hover:border-purple-500/50 transition-all duration-500">

                            <!-- Phone Notch -->
                            <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-8 bg-gray-900 rounded-b-2xl z-30 border-x-2 border-b-2 border-gray-700"></div>

                            <!-- Screen Content Area -->
                            <div class="relative w-full h-full p-6 pt-12 rounded-[46px] bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950 overflow-hidden">

                                <!-- Animated Status Bar -->
                                <div class="flex justify-between items-center mb-8 text-white/70 text-sm">
                                    <div class="flex items-center space-x-1">
                                        <div class="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                                        <span>Nashop</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-wifi text-xs"></i>
                                        <i class="fas fa-battery-three-quarters text-xs"></i>
                                    </div>
                                </div>

                                <!-- Interactive App Interface -->
                                <div class="space-y-6">

                                    <!-- Header with animated logo -->
                                    <div class="text-center mb-8">
                                        <div class="inline-block relative">
                                            <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-br from-purple-500 to-cyan-500 flex items-center justify-center animate-spin-slow">
                                                <i data-lucide="fingerprint" class="w-8 h-8 text-white"></i>
                                            </div>
                                            <h3 class="text-white font-bold text-lg bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">Nashop Mobile</h3>
                                        </div>
                                    </div>

                                    <!-- Animated Feature Cards -->
                                    <div class="space-y-4">
                                        <!-- Enhanced Shopping Card -->
                                        <div class="feature-card-mobile bg-gradient-to-r from-blue-900/40 to-purple-900/40 border border-blue-500/30 rounded-2xl p-4 transition-all duration-500 hover:border-blue-400/50 group/card will-change-transform">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center group-hover/card:bg-blue-500/30 transition-colors">
                                                    <i data-lucide="shopping-bag" class="w-5 h-5 text-blue-400"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="text-white font-semibold text-sm">Smart Shopping</h4>
                                                    <p class="text-gray-400 text-xs">AI-powered recommendations</p>
                                                </div>
                                                <div class="text-blue-400 text-xs animate-pulse">●</div>
                                            </div>
                                        </div>

                                        <!-- Token Card -->
                                        <div class="feature-card-mobile bg-gradient-to-r from-purple-900/40 to-fuchsia-900/40 border border-purple-500/30 rounded-2xl p-4 transform transition-all duration-500 hover:scale-105 hover:border-purple-400/50 group/card">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center group-hover/card:bg-purple-500/30 transition-colors">
                                                    <i data-lucide="coins" class="w-5 h-5 text-purple-400"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="text-white font-semibold text-sm">$DAN Rewards</h4>
                                                    <p class="text-gray-400 text-xs">Earn with every purchase</p>
                                                </div>
                                                <div class="text-purple-400 text-xs font-bold">+125</div>
                                            </div>
                                        </div>

                                        <!-- Wallet Card -->
                                        <div class="feature-card-mobile bg-gradient-to-r from-fuchsia-900/40 to-cyan-900/40 border border-fuchsia-500/30 rounded-2xl p-4 transform transition-all duration-500 hover:scale-105 hover:border-fuchsia-400/50 group/card">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 rounded-full bg-fuchsia-500/20 flex items-center justify-center group-hover/card:bg-fuchsia-500/30 transition-colors">
                                                    <i data-lucide="wallet" class="w-5 h-5 text-fuchsia-400"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="text-white font-semibold text-sm">Secure Wallet</h4>
                                                    <p class="text-gray-400 text-xs">Multi-chain support</p>
                                                </div>
                                                <div class="text-fuchsia-400 text-xs animate-pulse">🔒</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Interactive Bottom Section -->
                                    <div class="mt-8 pt-6 border-t border-gray-700/50">
                                        <div class="flex justify-center space-x-6">
                                            <div class="text-center group/icon cursor-pointer">
                                                <div class="w-12 h-12 mx-auto rounded-full bg-gradient-to-br from-purple-500/20 to-cyan-500/20 flex items-center justify-center group-hover/icon:from-purple-500/40 group-hover/icon:to-cyan-500/40 transition-all duration-300 group-hover/icon:scale-110">
                                                    <i data-lucide="home" class="w-6 h-6 text-purple-400"></i>
                                                </div>
                                                <span class="text-xs text-gray-500 mt-1 block">Home</span>
                                            </div>
                                            <div class="text-center group/icon cursor-pointer">
                                                <div class="w-12 h-12 mx-auto rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center group-hover/icon:from-blue-500/40 group-hover/icon:to-purple-500/40 transition-all duration-300 group-hover/icon:scale-110">
                                                    <i data-lucide="search" class="w-6 h-6 text-blue-400"></i>
                                                </div>
                                                <span class="text-xs text-gray-500 mt-1 block">Explore</span>
                                            </div>
                                            <div class="text-center group/icon cursor-pointer">
                                                <div class="w-12 h-12 mx-auto rounded-full bg-gradient-to-br from-fuchsia-500/20 to-cyan-500/20 flex items-center justify-center group-hover/icon:from-fuchsia-500/40 group-hover/icon:to-cyan-500/40 transition-all duration-300 group-hover/icon:scale-110">
                                                    <i data-lucide="user" class="w-6 h-6 text-fuchsia-400"></i>
                                                </div>
                                                <span class="text-xs text-gray-500 mt-1 block">Profile</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Floating Particles -->
                                <div class="absolute top-20 left-8 w-2 h-2 bg-purple-400/60 rounded-full animate-float-slow"></div>
                                <div class="absolute top-40 right-12 w-1 h-1 bg-cyan-400/60 rounded-full animate-float-slow-reverse"></div>
                                <div class="absolute bottom-32 left-12 w-1.5 h-1.5 bg-fuchsia-400/60 rounded-full animate-float-slow"></div>
                                <div class="absolute bottom-20 right-8 w-1 h-1 bg-blue-400/60 rounded-full animate-float-slow-reverse"></div>

                                <!-- Screen Reflection -->
                                <div class="absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-white/5 to-transparent opacity-30 pointer-events-none"></div>
                            </div>

                            <!-- Phone Frame Reflection -->
                            <div class="absolute top-[10%] left-[10%] right-[10%] h-[20%] bg-white/3 rounded-full blur-lg transform -rotate-12 opacity-40"></div>
                        </div>

                        <!-- Orbiting Elements -->
                        <div class="absolute -top-4 -left-4 w-8 h-8 bg-purple-500/20 rounded-full blur-sm animate-spin-slow"></div>
                        <div class="absolute -top-4 -right-4 w-6 h-6 bg-cyan-500/20 rounded-full blur-sm animate-spin-slow-reverse"></div>
                        <div class="absolute -bottom-4 -left-4 w-6 h-6 bg-fuchsia-500/20 rounded-full blur-sm animate-spin-slow"></div>
                        <div class="absolute -bottom-4 -right-4 w-8 h-8 bg-blue-500/20 rounded-full blur-sm animate-spin-slow-reverse"></div>

                        <!-- Data Flow Lines -->
                        <div class="absolute top-1/4 -left-20 w-16 h-0.5 bg-gradient-to-r from-transparent via-purple-500/50 to-transparent animate-pulse"></div>
                        <div class="absolute top-1/2 -right-20 w-16 h-0.5 bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent animate-pulse" style="animation-delay: 1s;"></div>
                        <div class="absolute bottom-1/4 -left-20 w-16 h-0.5 bg-gradient-to-r from-transparent via-fuchsia-500/50 to-transparent animate-pulse" style="animation-delay: 2s;"></div>
                    </div>
                </div>

                <!-- Right Side: Features and Download Links -->
                <div class="w-full lg:w-1/2 animate-fade-in-up" style="animation-delay: 0.4s;">
                    <!-- App Features -->
                    <div class="space-y-8">
                        <!-- Feature 1 -->
                        <div class="feature-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-blue-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-blue-500/10 transform hover:-translate-y-1">
                            <div class="flex items-start gap-4">
                                <div class="feature-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center group-hover:from-blue-500/30 group-hover:to-purple-500/30 transition-all duration-300">
                                    <i data-lucide="shopping-bag" class="w-6 h-6 text-blue-400 group-hover:text-blue-300 transition-colors"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-white mb-2">Shop Anywhere</h3>
                                    <p class="text-gray-400 leading-relaxed">Browse and purchase from thousands of merchants directly from your mobile device. Seamless checkout with multiple payment options.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Feature 2 -->
                        <div class="feature-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-purple-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-purple-500/10 transform hover:-translate-y-1">
                            <div class="flex items-start gap-4">
                                <div class="feature-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-purple-500/20 to-fuchsia-500/20 flex items-center justify-center group-hover:from-purple-500/30 group-hover:to-fuchsia-500/30 transition-all duration-300">
                                    <i data-lucide="wallet" class="w-6 h-6 text-purple-400 group-hover:text-purple-300 transition-colors"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-white mb-2">Manage $DAN Tokens</h3>
                                    <p class="text-gray-400 leading-relaxed">Track your token balance, view transaction history, and manage rewards all in one place with our secure mobile wallet.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Feature 3 -->
                        <div class="feature-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-fuchsia-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-fuchsia-500/10 transform hover:-translate-y-1">
                            <div class="flex items-start gap-4">
                                <div class="feature-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-fuchsia-500/20 to-cyan-500/20 flex items-center justify-center group-hover:from-fuchsia-500/30 group-hover:to-cyan-500/30 transition-all duration-300">
                                    <i data-lucide="bell" class="w-6 h-6 text-fuchsia-400 group-hover:text-fuchsia-300 transition-colors"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-white mb-2">Real-time Notifications</h3>
                                    <p class="text-gray-400 leading-relaxed">Get instant alerts for token rewards, exclusive deals, and limited-time offers. Never miss an opportunity to maximize your benefits.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- App Store Buttons -->
                    <div class="mt-10 flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4">
                        <!-- App Store Button -->
                        <button type="button" class="app-store-button store-button-trigger group relative inline-flex items-center justify-center gap-3 px-6 py-4 w-full sm:w-auto rounded-xl overflow-hidden transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50" data-store="App Store">
                            <!-- Button background with glass effect -->
                            <span class="absolute inset-0 bg-gray-900/80 backdrop-blur-md group-hover:bg-gray-800/90 transition-all duration-300"></span>

                            <!-- Button border glow -->
                            <span class="absolute inset-0 rounded-xl border-2 border-gray-700 group-hover:border-blue-500/50 group-hover:shadow-[0_0_15px_rgba(59,130,246,0.3)] transition-all duration-300"></span>

                            <!-- Button content -->
                            <span class="relative flex items-center">
                                <i class="fab fa-apple text-2xl text-white mr-2"></i>
                                <span class="flex flex-col">
                                    <span class="text-xs text-gray-400">Download on the</span>
                                    <span class="text-base font-semibold text-white">App Store</span>
                                </span>
                            </span>
                        </button>

                        <!-- Google Play Button -->
                        <button type="button" class="app-store-button store-button-trigger group relative inline-flex items-center justify-center gap-3 px-6 py-4 w-full sm:w-auto rounded-xl overflow-hidden transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50" data-store="Google Play">
                            <!-- Button background with glass effect -->
                            <span class="absolute inset-0 bg-gray-900/80 backdrop-blur-md group-hover:bg-gray-800/90 transition-all duration-300"></span>

                            <!-- Button border glow -->
                            <span class="absolute inset-0 rounded-xl border-2 border-gray-700 group-hover:border-blue-500/50 group-hover:shadow-[0_0_15px_rgba(59,130,246,0.3)] transition-all duration-300"></span>

                            <!-- Button content -->
                            <span class="relative flex items-center">
                                <i class="fab fa-google-play text-2xl text-white mr-2"></i>
                                <span class="flex flex-col">
                                    <span class="text-xs text-gray-400">GET IT ON</span>
                                    <span class="text-base font-semibold text-white">Google Play</span>
                                </span>
                            </span>
                        </button>
                    </div>

                    <!-- Coming Soon Notification (Hidden by default) -->
                    <div id="coming-soon-notification" class="fixed inset-0 flex items-center justify-center z-50 bg-black/70 backdrop-blur-md opacity-0 pointer-events-none transition-opacity duration-300">
                        <div class="notification-content relative max-w-md w-full mx-4 p-6 bg-gray-900/90 border border-blue-500/30 rounded-2xl shadow-2xl transform scale-95 transition-transform duration-300">
                            <!-- Decorative elements -->
                            <div class="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-fuchsia-500/20 rounded-2xl blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>

                            <!-- Close button -->
                            <button type="button" id="close-notification" class="absolute top-3 right-3 w-8 h-8 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors">
                                <i class="fas fa-times"></i>
                                <span class="sr-only">Close notification</span>
                            </button>

                            <!-- Content -->
                            <div class="relative text-center">
                                <!-- Icon -->
                                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-900/30 mb-4">
                                    <i class="fas fa-mobile-alt text-blue-400 text-2xl"></i>
                                </div>

                                <!-- Title -->
                                <h3 class="text-2xl font-bold text-white mb-2">Coming Soon!</h3>

                                <!-- Message -->
                                <p class="text-gray-300 mb-4">The Nashop mobile app will be available on <span id="store-name" class="text-blue-400 font-semibold">app stores</span> after the full launch.</p>

                                <!-- Waitlist CTA -->
                                <div class="mt-6">
                                    <button type="button" id="join-waitlist-btn" class="inline-flex items-center justify-center px-6 py-3 rounded-xl font-bold text-base text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300 ease-in-out transform hover:scale-[1.02] shadow-lg hover:shadow-blue-500/20 focus:outline-none focus:ring-4 focus:ring-blue-500/50">
                                        Join the Waitlist
                                        <i class="fas fa-arrow-right ml-2"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Coming Soon Badge -->
                    <div class="mt-6 flex justify-center lg:justify-start">
                        <div class="inline-flex items-center px-4 py-2 rounded-full bg-blue-900/30 border border-blue-500/30 text-sm text-blue-300">
                            <i class="fas fa-clock mr-2 text-xs"></i> Coming after full launch - Join waitlist for early access
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced QR Code Section -->
<div id="qr-code-section" class="mt-20 text-center" data-animate="fade-in" data-delay="600">
    <div class="inline-block relative p-8 bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-2xl hover-glow">
        <div class="absolute -inset-1 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-fuchsia-500/10 rounded-3xl blur-xl opacity-70 animate-pulse duration-[5000ms] will-change-opacity" aria-hidden="true"></div>

        <div class="relative flex flex-col md:flex-row items-center gap-6 md:gap-10">
            <!-- Enhanced QR Code Video -->
            <div class="qr-code-container relative w-40 h-40 rounded-xl shadow-lg overflow-hidden will-change-transform" data-animate="scale-in" data-delay="800">
                <!-- QR Code Video -->
                <video
                    id="qr-video"
                    src="img/QR VID.mp4"
                    class="w-full h-full rounded-xl object-cover transition-opacity duration-500"
                    muted
                    playsinline
                    preload="metadata"
                    poster=""
                    disablePictureInPicture
                    controlsList="nodownload nofullscreen noremoteplayback"
                    style="pointer-events: none;"
                >
                    Your browser does not support the video tag.
                </video>

                <!-- QR Generation Overlay -->
                <div id="qr-generation-overlay" class="absolute inset-0 bg-gradient-to-br from-gray-900/90 to-black/90 rounded-xl flex items-center justify-center text-white text-center p-4 backdrop-blur-sm">
                    <div>
                        <button
                            id="generate-qr-btn"
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm rounded-lg transition-all duration-200 shadow-lg border border-blue-500/50 flex items-center gap-2 mx-auto"
                            onclick="generateQRForMobile()"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Generate QR Code
                        </button>
                    </div>
                </div>


            </div>

            <!-- Enhanced Text Content -->
            <div class="text-left max-w-md" data-animate="slide-right" data-delay="1000">
                <h3 class="text-xl font-bold text-white mb-2" data-text-reveal="word-by-word" data-animate="text-reveal" data-delay="1200">
                    <span class="animate-gradient-text">Scan to Join Waitlist</span>
                </h3>
                <p class="text-gray-400 mb-4" data-animate="fade-in" data-delay="1400">Be among the first to experience the Nashop mobile app. Scan this code or visit our website to join the waitlist and receive exclusive early access benefits.</p>

                <!-- QR Status Indicator -->
                <div id="qr-status" class="mt-3 text-sm text-gray-500" style="display: none;">
                    <span id="qr-status-text">QR Code ready to scan!</span>
                </div>
            </div>
        </div>
    </div>
</div>
    </section>

    <!-- Token Economics Section -->
    <section id="tokenomics" aria-labelledby="tokenomics-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-cyan-950/10 to-black relative overflow-hidden">
        <!-- Enhanced Background Elements -->
        <div class="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
            <!-- Enhanced background gradients with animation -->
            <div class="absolute top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-cyan-600/15 via-blue-600/10 to-transparent rounded-full blur-[120px] opacity-60 translate-x-1/4 -translate-y-1/4 animate-pulse duration-[15000ms]"></div>
            <div class="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-blue-600/15 via-cyan-600/10 to-transparent rounded-full blur-[120px] opacity-60 -translate-x-1/4 translate-y-1/4 animate-pulse duration-[18000ms]"></div>

            <!-- Additional cosmic elements -->
            <div class="absolute top-1/3 left-1/4 w-32 h-32 bg-gradient-to-br from-cyan-500/10 to-transparent rounded-full blur-[50px] animate-float-slow" style="animation-duration: 25s;"></div>
            <div class="absolute bottom-1/4 right-1/3 w-40 h-40 bg-gradient-to-tl from-blue-500/10 to-transparent rounded-full blur-[60px] animate-float-slow-reverse" style="animation-duration: 30s;"></div>

            <!-- Subtle grid pattern overlay -->
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10"></div>

            <!-- Enhanced animated star field effect -->
            <div class="stars-container absolute inset-0 opacity-70">
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[25%] left-[30%] animate-twinkle" style="animation-delay: 0.2s; animation-duration: 3.5s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[45%] left-[70%] animate-twinkle" style="animation-delay: 1.2s; animation-duration: 4.5s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[65%] left-[20%] animate-twinkle" style="animation-delay: 2.1s; animation-duration: 3.8s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[15%] left-[80%] animate-twinkle" style="animation-delay: 1.5s; animation-duration: 4.2s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[85%] left-[40%] animate-twinkle" style="animation-delay: 0.8s; animation-duration: 4.0s;"></div>
            </div>

            <!-- Shooting stars -->
            <div class="shooting-star absolute w-[100px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[20%] left-[-10%] rotate-[15deg] animate-shooting-star" style="animation-delay: 3s;"></div>
            <div class="shooting-star absolute w-[150px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[40%] left-[-15%] rotate-[25deg] animate-shooting-star" style="animation-delay: 7s;"></div>

            <!-- Digital circuit lines -->
            <div class="absolute inset-0 opacity-20">
                <!-- Horizontal lines -->
                <div class="absolute top-1/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent"></div>
                <div class="absolute top-2/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"></div>
                <div class="absolute top-3/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent"></div>

                <!-- Vertical lines -->
                <div class="absolute left-1/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-blue-500/30 to-transparent"></div>
                <div class="absolute left-2/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-cyan-500/30 to-transparent"></div>
                <div class="absolute left-3/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-blue-500/30 to-transparent"></div>
            </div>
        </div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <div class="relative inline-block mb-4" data-animate="scale-in" data-delay="400">
                    <!-- Glow effect behind heading -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-cyan-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                    <h2 id="tokenomics-heading" class="relative text-4xl md:text-5xl lg:text-6xl font-bold mb-4 uppercase tracking-tighter" data-text-reveal="letter-by-letter" data-animate="text-reveal" data-delay="600">
                        <span class="bg-gradient-to-r from-cyan-400 via-blue-400 to-cyan-400 text-transparent bg-clip-text animate-gradient-x">
                            $DAN Tokenomics
                        </span>
                    </h2>
                </div>

                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto" data-animate="slide-up" data-delay="800">
                    The $DAN token powers the Nashop ecosystem, providing utility, governance, and rewards.
                </p>

                <!-- Decorative Divider -->
                <div class="w-24 h-1 bg-gradient-to-r from-cyan-500 via-blue-500 to-cyan-500 mx-auto mt-6 rounded-full" data-animate="scale-in" data-delay="1000"></div>
            </div>

            <!-- Token Distribution Chart -->
            <div class="flex flex-col lg:flex-row items-center justify-between gap-12 lg:gap-6 max-w-6xl mx-auto">
                <!-- Left Side: Interactive Donut Chart -->
                <div class="w-full lg:w-1/2 relative animate-fade-in-up" style="animation-delay: 0.2s;">
                    <div class="chart-container relative mx-auto w-[280px] h-[280px] md:w-[320px] md:h-[320px] perspective-1000">
                        <!-- Decorative elements -->
                        <div class="absolute -inset-4 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-cyan-500/10 rounded-full blur-xl opacity-50 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                        <!-- Orbital ring -->
                        <div class="absolute inset-0 w-full h-full rounded-full border border-cyan-500/30 animate-spin-slow opacity-70" style="width: calc(100% + 30px); height: calc(100% + 30px); top: -15px; left: -15px;"></div>
                        <div class="absolute inset-0 w-full h-full rounded-full border border-blue-500/20 animate-spin-slow-reverse opacity-50" style="width: calc(100% + 50px); height: calc(100% + 50px); top: -25px; left: -25px;"></div>

                        <!-- Interactive Donut Chart -->
                        <div class="donut-chart relative w-full h-full rounded-full overflow-hidden bg-gray-900/60 backdrop-blur-sm border-4 border-gray-800/50 shadow-2xl transform transition-all duration-500 hover:scale-105">
                            <!-- Chart Segments -->
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(0deg);" data-percentage="30" data-label="Shopping Rewards"></div>
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(108deg);" data-percentage="30" data-label="Locked Ecosystem Reserve"></div>
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-purple-500 to-fuchsia-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(216deg);" data-percentage="10" data-label="Team"></div>
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-fuchsia-500 to-pink-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(252deg);" data-percentage="10" data-label="Community & Marketing"></div>
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-pink-500 to-cyan-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(288deg);" data-percentage="10" data-label="Liquidity Pool"></div>
                            <div class="chart-segment absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500" style="clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%); transform: rotate(324deg);" data-percentage="10" data-label="Presale"></div>

                            <!-- Center Circle -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60%] h-[60%] rounded-full bg-gray-900/80 backdrop-blur-md border-2 border-gray-800/50 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 text-transparent bg-clip-text">$DAN</div>
                                    <div class="text-xs text-gray-400 mt-1">Total Supply: 10M</div>
                                </div>
                            </div>
                        </div>

                        <!-- Animated data points -->
                        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-cyan-400/70 rounded-full animate-ping opacity-70 duration-[2000ms]"></div>
                        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-400/70 rounded-full animate-ping opacity-70 duration-[2500ms]" style="animation-delay: 0.5s;"></div>
                    </div>

                    <!-- Legend -->
                    <div class="mt-10 grid grid-cols-2 md:grid-cols-3 gap-4 max-w-md mx-auto">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500"></div>
                            <span class="text-sm text-gray-300">Shopping Rewards (30%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                            <span class="text-sm text-gray-300">Ecosystem Reserve (30%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-purple-500 to-fuchsia-500"></div>
                            <span class="text-sm text-gray-300">Team (10%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-fuchsia-500 to-pink-500"></div>
                            <span class="text-sm text-gray-300">Community & Marketing (10%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-pink-500 to-cyan-500"></div>
                            <span class="text-sm text-gray-300">Liquidity Pool (10%)</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500"></div>
                            <span class="text-sm text-gray-300">Presale (10%)</span>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Token Details -->
                <div class="w-full lg:w-1/2 animate-fade-in-up" style="animation-delay: 0.4s;">
                    <!-- Token Metrics -->
                    <div class="p-6 bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl mb-8">
                        <h3 class="text-xl font-bold text-white mb-4">Token Metrics</h3>
                        <div class="grid grid-cols-2 gap-6">
                            <div class="flex flex-col">
                                <span class="text-sm text-gray-400">Token Name</span>
                                <span class="text-lg font-medium text-cyan-300">$DAN</span>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-sm text-gray-400">Total Supply</span>
                                <span class="text-lg font-medium text-cyan-300">10,000,000</span>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-sm text-gray-400">Network</span>
                                <span class="text-lg font-medium text-cyan-300">Polygon (POL-20)</span>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-sm text-gray-400">Decimal Precision</span>
                                <span class="text-lg font-medium text-cyan-300">18</span>
                            </div>
                        </div>
                    </div>

                    <!-- Vesting Schedule -->
                    <div class="p-6 bg-gray-900/40 backdrop-blur-sm border border-gray-800 rounded-xl">
                        <h3 class="text-xl font-bold text-white mb-4">Vesting Schedule</h3>
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-cyan-500/20 to-blue-500/20 flex items-center justify-center">
                                    <i class="fas fa-shopping-cart text-xs text-cyan-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Shopping Rewards (30%)</h4>
                                    <p class="text-sm text-gray-400">Gradual release based on platform activity. Algorithmically released as users earn rewards.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                                    <i class="fas fa-lock text-xs text-blue-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Ecosystem Reserve (30%)</h4>
                                    <p class="text-sm text-gray-400">Locked for 5 years. Held in a secure multi-signature wallet for long-term ecosystem health.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-purple-500/20 to-fuchsia-500/20 flex items-center justify-center">
                                    <i class="fas fa-users text-xs text-purple-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Team (10%)</h4>
                                    <p class="text-sm text-gray-400">6-month cliff, then linear vesting over 18 months. Total 2-year vesting period including cliff.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-fuchsia-500/20 to-pink-500/20 flex items-center justify-center">
                                    <i class="fas fa-bullhorn text-xs text-fuchsia-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Community & Marketing (10%)</h4>
                                    <p class="text-sm text-gray-400">10% at TGE, remaining 90% vested linearly over 12 months. Monthly or quarterly unlocks.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-pink-500/20 to-cyan-500/20 flex items-center justify-center">
                                    <i class="fas fa-exchange-alt text-xs text-pink-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Liquidity Pool (10%)</h4>
                                    <p class="text-sm text-gray-400">Locked for 2 years. Managed via a time-locked contract to guarantee liquidity availability.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="utility-icon flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-cyan-500/20 to-blue-500/20 flex items-center justify-center">
                                    <i class="fas fa-rocket text-xs text-cyan-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-base font-semibold text-white">Presale (10%)</h4>
                                    <p class="text-sm text-gray-400">100% unlocked at TGE (Token Generation Event). Immediate access for early supporters.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CTA Button -->
            <div class="mt-16 text-center animate-fade-in-up" style="animation-delay: 0.6s;">
                <div class="inline-block relative">
                    <div class="absolute -inset-1 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-cyan-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>
                    <a href="https://token.nashop.store" target="_blank" rel="noopener noreferrer" class="relative inline-flex items-center justify-center px-8 py-3 rounded-full text-base font-medium text-white bg-gradient-to-r from-cyan-600/40 to-blue-600/40 border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-[1.02] shadow-lg shadow-cyan-900/10">
                        <span class="mr-2">Learn More About $DAN Token</span>
                        <i class="fas fa-external-link-alt text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Comparison Table Section -->
    <section id="comparison" aria-labelledby="comparison-heading" class="py-20 md:py-32 bg-gradient-to-b from-black via-indigo-950/10 to-black relative overflow-hidden">
        <!-- Enhanced Background Elements -->
        <div class="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
            <!-- Enhanced background gradients with animation -->
            <div class="absolute top-0 right-0 w-2/3 h-2/3 bg-gradient-to-bl from-indigo-600/15 via-violet-600/10 to-transparent rounded-full blur-[120px] opacity-60 translate-x-1/4 -translate-y-1/4 animate-pulse duration-[15000ms]"></div>
            <div class="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-violet-600/15 via-indigo-600/10 to-transparent rounded-full blur-[120px] opacity-60 -translate-x-1/4 translate-y-1/4 animate-pulse duration-[18000ms]"></div>

            <!-- Additional cosmic elements -->
            <div class="absolute top-1/3 left-1/4 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-transparent rounded-full blur-[50px] animate-float-slow" style="animation-duration: 25s;"></div>
            <div class="absolute bottom-1/4 right-1/3 w-40 h-40 bg-gradient-to-tl from-violet-500/10 to-transparent rounded-full blur-[60px] animate-float-slow-reverse" style="animation-duration: 30s;"></div>

            <!-- Subtle grid pattern overlay -->
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%20%2F%3E%3C%2Fsvg%3E')] opacity-10"></div>

            <!-- Enhanced animated star field effect -->
            <div class="stars-container absolute inset-0 opacity-70">
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[25%] left-[30%] animate-twinkle" style="animation-delay: 0.2s; animation-duration: 3.5s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[45%] left-[70%] animate-twinkle" style="animation-delay: 1.2s; animation-duration: 4.5s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[65%] left-[20%] animate-twinkle" style="animation-delay: 2.1s; animation-duration: 3.8s;"></div>
                <div class="star absolute w-[2px] h-[2px] bg-white rounded-full top-[15%] left-[80%] animate-twinkle" style="animation-delay: 1.5s; animation-duration: 4.2s;"></div>
                <div class="star absolute w-[1px] h-[1px] bg-white rounded-full top-[85%] left-[40%] animate-twinkle" style="animation-delay: 0.8s; animation-duration: 4.0s;"></div>
            </div>

            <!-- Shooting stars -->
            <div class="shooting-star absolute w-[100px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[20%] left-[-10%] rotate-[15deg] animate-shooting-star" style="animation-delay: 3s;"></div>
            <div class="shooting-star absolute w-[150px] h-[1px] bg-gradient-to-r from-white via-white to-transparent top-[40%] left-[-15%] rotate-[25deg] animate-shooting-star" style="animation-delay: 7s;"></div>

            <!-- Digital circuit lines -->
            <div class="absolute inset-0 opacity-20">
                <!-- Horizontal lines -->
                <div class="absolute top-1/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
                <div class="absolute top-2/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-violet-500/30 to-transparent"></div>
                <div class="absolute top-3/4 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>

                <!-- Vertical lines -->
                <div class="absolute left-1/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-violet-500/30 to-transparent"></div>
                <div class="absolute left-2/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-indigo-500/30 to-transparent"></div>
                <div class="absolute left-3/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-violet-500/30 to-transparent"></div>
            </div>
        </div>

        <div class="container mx-auto px-4 md:px-6 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-16 md:mb-20" data-animate="fade-in" data-delay="200">
                <div class="relative inline-block mb-4" data-animate="scale-in" data-delay="400">
                    <!-- Glow effect behind heading -->
                    <div class="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-violet-500/20 to-indigo-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[5000ms]" aria-hidden="true"></div>

                    <h2 id="comparison-heading" class="relative text-4xl md:text-5xl lg:text-6xl font-bold mb-4 uppercase tracking-tighter" data-text-reveal="word-by-word" data-animate="text-reveal" data-delay="600">
                        <span class="bg-gradient-to-r from-indigo-400 via-violet-400 to-indigo-400 text-transparent bg-clip-text animate-gradient-x">
                            Why Choose Nashop
                        </span>
                    </h2>
                </div>

                <p class="text-gray-400 text-lg md:text-xl opacity-90 max-w-3xl mx-auto" data-animate="slide-up" data-delay="800">
                    See how Nashop compares to traditional e-commerce platforms and other Web3 marketplaces.
                </p>

                <!-- Decorative Divider -->
                <div class="w-24 h-1 bg-gradient-to-r from-indigo-500 via-violet-500 to-indigo-500 mx-auto mt-6 rounded-full" data-animate="scale-in" data-delay="1000"></div>
            </div>

            <!-- Comparison Table -->
            <div class="max-w-6xl mx-auto overflow-hidden rounded-xl border border-gray-800 bg-gray-900/40 backdrop-blur-sm animate-fade-in-up" style="animation-delay: 0.2s;">
                <!-- Table Header - Enhanced for all devices -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6 border-b border-gray-800 bg-gray-900/60">
                    <!-- Feature Column - Hidden on mobile, shown on larger screens -->
                    <div class="hidden lg:block">
                        <div class="text-center lg:text-left">
                            <span class="text-xl md:text-2xl font-bold text-gray-300">Features</span>
                        </div>
                    </div>

                    <!-- Nashop Column -->
                    <div class="text-center">
                        <div class="relative inline-block">
                            <!-- Glow effect -->
                            <div class="absolute -inset-2 bg-gradient-to-r from-indigo-500/20 to-violet-500/20 rounded-lg blur-md opacity-70" aria-hidden="true"></div>
                            <div class="relative">
                                <span class="text-xl md:text-2xl font-bold bg-gradient-to-r from-indigo-400 to-violet-400 text-transparent bg-clip-text">Nashop</span>
                                <div class="mt-1 px-3 py-1 text-xs font-medium text-white bg-gradient-to-r from-indigo-600/40 to-violet-600/40 rounded-full inline-block">
                                    Web3 Native
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Traditional E-commerce Column -->
                    <div class="text-center">
                        <span class="text-xl md:text-2xl font-bold text-gray-300">Traditional<br class="hidden sm:inline">E-commerce</span>
                    </div>

                    <!-- Other Web3 Marketplaces Column -->
                    <div class="text-center">
                        <span class="text-xl md:text-2xl font-bold text-gray-300">Other Web3<br class="hidden sm:inline">Marketplaces</span>
                    </div>
                </div>

                <!-- Table Body -->
                <div class="divide-y divide-gray-800">
                    <!-- Row 1: Transaction Fees - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="transaction-fees-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-percentage text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">Transaction Fees</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">1-3%</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Lowest in the industry</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">5-15%</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Plus payment processing</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">2-10%</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Plus gas fees</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="transaction-fees-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">1-3%</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Lowest in the industry</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">5-15%</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Plus payment processing</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">2-10%</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Plus gas fees</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 2: Reward System - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="reward-system-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-gift text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">Reward System</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">$DAN Tokens</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Real value & utility</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Points/Cashback</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Limited value</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Varies</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Often minimal</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="reward-system-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">$DAN Tokens</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Real value & utility</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Points/Cashback</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Limited value</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Varies</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Often minimal</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 3: Data Ownership - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="data-ownership-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-user-shield text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">Data Ownership</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">User Controlled</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Full privacy</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Platform Owned</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Sold to advertisers</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Mixed</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Varies by platform</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="data-ownership-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">User Controlled</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Full privacy</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Platform Owned</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Sold to advertisers</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Mixed</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Varies by platform</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 4: Payment Options - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="payment-options-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-credit-card text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">Payment Options</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">Crypto + Fiat</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Best of both worlds</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Fiat Only</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Limited options</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Crypto Only</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Limited adoption</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="payment-options-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">Crypto + Fiat</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Best of both worlds</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Fiat Only</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Limited options</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Crypto Only</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Limited adoption</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 5: Governance - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="governance-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-vote-yea text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">Governance</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">Community DAO</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Token-based voting</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Centralized</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Corporate control</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Varies</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Often limited</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="governance-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">Community DAO</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Token-based voting</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Centralized</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Corporate control</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Varies</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Often limited</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 6: User Experience - Enhanced for all devices -->
                    <div class="p-6 hover:bg-gray-900/30 transition-colors duration-300">
                        <!-- Feature Title - Clickable to expand/collapse on mobile, static on desktop -->
                        <div class="cursor-pointer lg:cursor-default comparison-feature-toggle" data-target="user-experience-content">
                            <div class="flex items-center gap-3 justify-center lg:justify-start">
                                <div class="feature-icon flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center">
                                    <i class="fas fa-smile text-indigo-400"></i>
                                </div>
                                <span class="font-medium text-white text-lg">User Experience</span>
                                <!-- Toggle indicator - rotates when expanded (mobile only) -->
                                <i class="fas fa-chevron-down text-indigo-400 transition-transform duration-300 ml-auto lg:hidden"></i>
                            </div>
                        </div>

                        <!-- Desktop layout - Grid with 4 columns -->
                        <div class="hidden lg:grid lg:grid-cols-4 lg:gap-4 lg:mt-4 lg:items-center">
                            <!-- Empty column for alignment with header -->
                            <div class="lg:col-span-1"></div>

                            <!-- Nashop Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-bold text-indigo-400">Web2 Simplicity</span>
                                <p class="text-xs md:text-sm text-gray-400 mt-1">Web3 benefits</p>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Simple</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">But centralized</p>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="lg:col-span-1 text-center">
                                <span class="text-lg md:text-xl font-medium text-gray-300">Complex</span>
                                <p class="text-xs md:text-sm text-gray-500 mt-1">Technical barriers</p>
                            </div>
                        </div>

                        <!-- Mobile layout - Collapsible content -->
                        <div id="user-experience-content" class="comparison-content hidden lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 mt-4 transition-all duration-300">
                            <!-- Nashop Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-indigo-400 mb-1">Nashop</div>
                                    <span class="text-lg md:text-xl font-bold text-indigo-400">Web2 Simplicity</span>
                                    <p class="text-xs md:text-sm text-gray-400 mt-1">Web3 benefits</p>
                                </div>
                            </div>

                            <!-- Traditional E-commerce Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Traditional E-commerce</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Simple</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">But centralized</p>
                                </div>
                            </div>

                            <!-- Other Web3 Marketplaces Column -->
                            <div class="flex flex-col items-center p-3 rounded-lg bg-gray-900/40">
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-400 mb-1">Other Web3 Marketplaces</div>
                                    <span class="text-lg md:text-xl font-medium text-gray-300">Complex</span>
                                    <p class="text-xs md:text-sm text-gray-500 mt-1">Technical barriers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Benefits Summary -->
            <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <!-- Benefit 1 -->
                <div class="benefit-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-indigo-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-indigo-500/10 transform hover:-translate-y-1 animate-fade-in-up" style="animation-delay: 0.4s;">
                    <div class="flex items-start gap-4">
                        <div class="benefit-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center group-hover:from-indigo-500/30 group-hover:to-violet-500/30 transition-all duration-300">
                            <i class="fas fa-coins w-6 h-6 text-indigo-400 group-hover:text-indigo-300 transition-colors"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">Lower Costs</h3>
                            <p class="text-gray-400 leading-relaxed">Save up to 80% on transaction fees compared to traditional platforms, with transparent pricing and no hidden charges.</p>
                        </div>
                    </div>
                </div>

                <!-- Benefit 2 -->
                <div class="benefit-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-violet-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-violet-500/10 transform hover:-translate-y-1 animate-fade-in-up" style="animation-delay: 0.5s;">
                    <div class="flex items-start gap-4">
                        <div class="benefit-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-violet-500/20 to-indigo-500/20 flex items-center justify-center group-hover:from-violet-500/30 group-hover:to-indigo-500/30 transition-all duration-300">
                            <i class="fas fa-shield-alt w-6 h-6 text-violet-400 group-hover:text-violet-300 transition-colors"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">True Ownership</h3>
                            <p class="text-gray-400 leading-relaxed">Maintain control of your data and digital assets with blockchain-backed security and user-centric privacy protection.</p>
                        </div>
                    </div>
                </div>

                <!-- Benefit 3 -->
                <div class="benefit-card group relative bg-gray-900/40 backdrop-blur-sm border border-gray-800 hover:border-indigo-500/50 rounded-xl p-6 transition-all duration-300 hover:bg-gray-900/60 hover:shadow-lg hover:shadow-indigo-500/10 transform hover:-translate-y-1 animate-fade-in-up" style="animation-delay: 0.6s;">
                    <div class="flex items-start gap-4">
                        <div class="benefit-icon flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500/20 to-violet-500/20 flex items-center justify-center group-hover:from-indigo-500/30 group-hover:to-violet-500/30 transition-all duration-300">
                            <i class="fas fa-rocket w-6 h-6 text-indigo-400 group-hover:text-indigo-300 transition-colors"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">Future-Proof</h3>
                            <p class="text-gray-400 leading-relaxed">Join the next generation of e-commerce with Web3 technology that offers better security, transparency, and community governance.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CTA Button -->
            <div class="mt-16 text-center animate-fade-in-up" style="animation-delay: 0.7s;">
                <div class="inline-block relative">
                    <div class="absolute -inset-1 bg-gradient-to-r from-indigo-500/20 via-violet-500/20 to-indigo-500/20 rounded-lg blur-xl opacity-70 animate-pulse duration-[3000ms]" aria-hidden="true"></div>
                    <a href="#features" class="relative inline-flex items-center justify-center px-8 py-3 rounded-full text-base font-medium text-white bg-gradient-to-r from-indigo-600/40 to-violet-600/40 border border-white/10 hover:border-white/20 transition-all duration-300 transform hover:scale-[1.02] shadow-lg shadow-indigo-900/10">
                        <span class="mr-2">Explore Nashop Features</span>
                        <i class="fas fa-arrow-right text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Join/Newsletter Section -->
    <section id="join" aria-labelledby="join-heading" class="py-20 md:py-32 bg-black relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 z-0 opacity-[0.06] bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill%3D%22%239C92AC%22%20fill-opacity%3D%220.4%22%20fill-rule%3D%22evenodd%22%3E%3Cpath%20d%3D%22M0%200h40v1H0zM0%2040h40v1H0zM0%200v40h1V0zm40%200v40h1V0z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E')]" aria-hidden="true"></div>
        <!-- Background Glow -->
        <div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-bl from-purple-900/10 to-transparent blur-[100px] opacity-30 -z-10" aria-hidden="true"></div>

        <div class="container mx-auto px-4 md:px-6 text-center relative z-10">
            <div class="animate-fade-in-up">
                <i data-lucide="mail-check" class="w-12 h-12 text-purple-400 mx-auto mb-6 transition-transform hover:scale-110 duration-300" aria-label="Newsletter icon"></i>
                <h2 id="join-heading" class="text-3xl md:text-4xl lg:text-5xl font-bold mb-5 text-white tracking-tight">Stay Connected</h2>
                <p class="text-gray-400 text-lg opacity-90 max-w-2xl mx-auto mb-10">Be the first to know about project milestones, token news, and platform updates. Subscribe to our newsletter.</p>

                <form id="newsletter-form" class="flex flex-col sm:flex-row gap-3 max-w-xl mx-auto relative" aria-labelledby="join-heading">
                    <label for="email" class="sr-only">Email Address</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required aria-label="Email for newsletter" aria-describedby="subscription-message"
                           class="flex-1 px-6 py-4 rounded-full bg-gray-900/80 border-2 border-gray-700 text-white placeholder-gray-500 focus:outline-none focus:border-purple-500/80 focus:ring-0 transition duration-300 text-base shadow-inner appearance-none backdrop-blur-sm pr-36">
                    <button type="submit" id="subscribe-button" aria-label="Subscribe to newsletter"
                            class="sm:absolute sm:right-1.5 sm:top-1.5 sm:bottom-1.5 px-8 sm:px-6 py-3 rounded-full font-semibold text-white bg-gradient-to-r from-purple-600 to-fuchsia-600 hover:from-purple-700 hover:to-fuchsia-700 transition-all duration-300 ease-in-out transform hover:scale-[1.04] shadow-md hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-purple-500/50 whitespace-nowrap">
                        Subscribe
                    </button>
                </form>
                <p id="subscription-message" role="status" class="mt-4 text-sm text-green-400 hidden h-5"></p>

                <!-- Social Links -->
                <div class="mt-16 flex justify-center space-x-6 sm:space-x-8" role="navigation" aria-label="Social media links">
                    <a href="https://x.com/nashopstoreweb3" target="_blank" rel="noopener noreferrer" title="Nashop on Twitter/X" class="text-gray-500 hover:text-sky-400 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1">
                        <i class="fab fa-x-twitter text-2xl" aria-hidden="true"></i> <span class="sr-only">Twitter/X</span>
                    </a>
                    <a href="https://www.instagram.com/nashop.store.web3/" target="_blank" rel="noopener noreferrer" title="Nashop on Instagram" class="text-gray-500 hover:text-pink-400 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1">
                        <i class="fab fa-instagram text-2xl" aria-hidden="true"></i> <span class="sr-only">Instagram</span>
                    </a>
                    <a href="https://t.me/dan_nashop" target="_blank" rel="noopener noreferrer" title="Nashop Telegram Group" class="text-gray-500 hover:text-blue-500 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1">
                        <i class="fab fa-telegram text-2xl" aria-hidden="true"></i> <span class="sr-only">Telegram</span>
                    </a>
                    <a href="mailto:<EMAIL>" title="Email Nashop Contact" class="text-gray-500 hover:text-gray-300 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1">
                        <i class="fas fa-envelope text-2xl" aria-hidden="true"></i> <span class="sr-only">Email Contact</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black py-10 border-t border-white/10" role="contentinfo">
        <div class="container mx-auto px-4 md:px-6 text-center">
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <p class="text-xs text-gray-500 opacity-80">
                    © <span id="current-year"></span> NASHOP Protocol. Decentralizing Commerce.
                </p>
                 <p>Contact CEO: <a href="mailto:<EMAIL>" class="text-fuchsia-300 hover:underline"><EMAIL></a></p>
                <nav aria-label="Footer links" class="flex flex-wrap justify-center items-center gap-x-4 gap-y-1 text-xs">
                    <a href="privacy-policy.html" class="text-gray-500 hover:text-gray-300 transition-colors duration-200">Privacy Policy</a>
                    <a href="terms.html" class="text-gray-500 hover:text-gray-300 transition-colors duration-200">Terms of Service</a>
                    <a href="faq.html" class="text-gray-500 hover:text-gray-300 transition-colors duration-200">FAQ</a>
                    <a href="#top" class="text-gray-500 hover:text-gray-300 transition-colors duration-200 inline-flex items-center">Back to Top <i class="fas fa-arrow-up text-[10px] ml-1.5" aria-hidden="true"></i></a>
                </nav>
            </div>
        </div>
    </footer>

    <!-- ===== START: Scroll Navigation Element ===== -->
    <nav id="scroll-nav" aria-label="Page Section Navigation">
      <!-- Animated background glow effects -->
      <div class="scroll-nav-glow-container">
        <div class="scroll-nav-glow scroll-nav-glow-1"></div>
        <div class="scroll-nav-glow scroll-nav-glow-2"></div>
        <div class="scroll-nav-particles">
          <span></span><span></span><span></span><span></span><span></span>
        </div>
      </div>

      <div class="scroll-nav-wrapper">
        <!-- Enhanced logo with glow effect -->
        <a href="#top" class="scroll-nav-logo-link" aria-label="Scroll to Top">
          <div class="scroll-nav-logo-glow"></div>
          <img src="logo/scrol_img.png" alt="Scroll Image" width="52.012" height="51.679" class="scroll-nav-logo">
        </a>

        <div class="scroll-nav-nodes">
            <!-- Enhanced progress bar with animated gradient -->
            <div class="scroll-nav-line">
              <div class="scroll-nav-blue-line">
                <div class="scroll-nav-red-container">
                  <div class="scroll-nav-red-line"></div>
                </div>
              </div>
            </div>

            <!-- Node 1: Linked to #features -->
            <div class="scroll-nav-node" data-target="#features" data-label="Features" aria-label="Scroll to Features section">
              <div class="scroll-nav-tooltip">Features</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 2: Linked to #roadmap -->
            <div class="scroll-nav-node" data-target="#roadmap" data-label="Roadmap" aria-label="Scroll to Roadmap section">
              <div class="scroll-nav-tooltip">Roadmap</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 3: Linked to #partners -->
            <div class="scroll-nav-node" data-target="#partners" data-label="Partners" aria-label="Scroll to Partners section">
              <div class="scroll-nav-tooltip">Partners</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 4: Linked to #team -->
            <div class="scroll-nav-node" data-target="#team" data-label="Team" aria-label="Scroll to Team section">
              <div class="scroll-nav-tooltip">Team</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 5: Linked to #mobile-app -->
            <div class="scroll-nav-node" data-target="#mobile-app" data-label="Mobile" aria-label="Scroll to Mobile App section">
              <div class="scroll-nav-tooltip">Mobile App</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 6: Linked to #tokenomics -->
            <div class="scroll-nav-node" data-target="#tokenomics" data-label="Token" aria-label="Scroll to Tokenomics section">
              <div class="scroll-nav-tooltip">Tokenomics</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 7: Linked to #comparison -->
            <div class="scroll-nav-node" data-target="#comparison" data-label="Compare" aria-label="Scroll to Comparison section">
              <div class="scroll-nav-tooltip">Comparison</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>

            <!-- Node 8: Linked to #join -->
            <div class="scroll-nav-node" data-target="#join" data-label="Connect" aria-label="Scroll to Join section">
              <div class="scroll-nav-tooltip">Connect</div>
              <div class="scroll-nav-active-circle">
                <div class="scroll-nav-splash"></div>
                <div class="scroll-nav-cover">
                  <div class="scroll-nav-white-dot"></div>
                </div>
              </div>
              <div class="scroll-nav-inactive-circle"></div>
            </div>
          </div>

          <!-- Current section indicator -->
          <div class="scroll-nav-current-section">
            <span id="scroll-nav-section-name">Explore</span>
          </div>
        </div>
    </nav>
    <!-- ===== END: Scroll Navigation Element ===== -->

    <!-- JavaScript files loaded in head section -->

    <!-- QR Server Configuration -->
    <script>
        // Configure QR server URL for PythonAnywhere
        window.QR_SERVER_URL = 'https://nourddinak.pythonanywhere.com';
    </script>

    <!-- QR Code Helper Functions -->
    <script>
        // Helper functions for QR code generation and management
        async function generateQRForMobile() {
            if (!window.qrNotificationHandler) {
                console.error('QR Notification Handler not initialized');
                alert('QR system not ready. Please refresh the page.');
                return;
            }

            try {
                // Show loading state
                const generateBtn = document.getElementById('generate-qr-btn');
                const qrStatus = document.getElementById('qr-status');
                const qrStatusText = document.getElementById('qr-status-text');
                const qrOverlay = document.getElementById('qr-generation-overlay');

                generateBtn.innerHTML = `
                    <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Generating...
                `;
                generateBtn.disabled = true;

                // Generate QR for Nashop mobile app
                const result = await window.qrNotificationHandler.createQRForURL('https://mobile.nashop.store/');

                if (result) {
                    console.log('QR Code generated for mobile app:', result);

                    // Hide the generation overlay permanently
                    qrOverlay.style.opacity = '0';
                    setTimeout(() => {
                        qrOverlay.style.display = 'none';
                    }, 500);



                    // Show success notification
                    showQRNotification('QR Code generated! Scan to visit Nashop mobile app.', 'success');
                } else {
                    throw new Error('Failed to generate QR code');
                }
            } catch (error) {
                console.error('Error generating QR:', error);
                showQRNotification('Failed to generate QR code. Make sure the server is running.', 'error');

                // Reset button
                const generateBtn = document.getElementById('generate-qr-btn');
                generateBtn.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Generate QR Code
                `;
                generateBtn.disabled = false;
            }
        }



        function showQRNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            } text-white`;

            notification.textContent = message;
            notification.style.transform = 'translate(-50%, -100px)';
            notification.style.opacity = '0';

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translate(-50%, 0)';
                notification.style.opacity = '1';
            }, 100);

            // Auto-remove after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translate(-50%, -100px)';
                notification.style.opacity = '0';

                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Add custom scan notification handler
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 QR System: Page loaded, checking for QR buttons...');

            // Check if buttons exist
            const generateBtn = document.getElementById('generate-qr-btn');
            const hideBtn = document.getElementById('hide-qr-btn');

            if (generateBtn) {
                console.log('✅ QR System: Generate button found!');
            } else {
                console.error('❌ QR System: Generate button NOT found!');
            }

            // Wait for QR handler to initialize
            setTimeout(() => {
                if (window.qrNotificationHandler) {
                    console.log('✅ QR System: Notification handler initialized!');
                    window.qrNotificationHandler.onScanNotification((scanData) => {
                        console.log('Custom scan handler triggered:', scanData);

                        // Add a special effect to the page
                        document.body.style.background = 'linear-gradient(45deg, #000, #001a1a, #000)';
                        setTimeout(() => {
                            document.body.style.background = '';
                        }, 2000);
                    });
                } else {
                    console.error('❌ QR System: Notification handler NOT initialized!');
                }
            }, 1000);
        });
    </script>

</body>
</html>
