// performance-test.js - Comprehensive Animation Performance Testing Suite

class PerformanceTestSuite {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
        this.testStartTime = 0;
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this.fpsHistory = [];
        this.memoryHistory = [];
        
        this.init();
    }
    
    init() {
        this.createTestUI();
        this.setupEventListeners();
        
        // Auto-run basic tests if debug mode is enabled
        if (window.location.search.includes('perf-test=true')) {
            setTimeout(() => this.runAllTests(), 2000);
        }
    }
    
    createTestUI() {
        const testUI = document.createElement('div');
        testUI.id = 'performance-test-ui';
        testUI.className = 'performance-test-ui';
        testUI.innerHTML = `
            <div class="test-header">
                <h3>🚀 Animation Performance Test Suite</h3>
                <button id="run-all-tests" class="btn-test">Run All Tests</button>
                <button id="run-basic-test" class="btn-test">Basic Test</button>
                <button id="clear-results" class="btn-test">Clear</button>
                <button id="export-results" class="btn-test">Export</button>
            </div>
            <div class="test-metrics">
                <div class="metric">
                    <span class="metric-label">FPS:</span>
                    <span id="current-fps" class="metric-value">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory:</span>
                    <span id="current-memory" class="metric-value">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Animations:</span>
                    <span id="active-animations" class="metric-value">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Device:</span>
                    <span id="device-type" class="metric-value">${this.getDeviceType()}</span>
                </div>
            </div>
            <div id="test-results" class="test-results"></div>
        `;
        
        // Add CSS styles
        const styles = document.createElement('style');
        styles.textContent = `
            .performance-test-ui {
                position: fixed;
                top: 10px;
                left: 10px;
                width: 350px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                z-index: 10000;
                display: none;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .performance-test-ui.active {
                display: block;
            }
            
            .test-header {
                margin-bottom: 10px;
                border-bottom: 1px solid #333;
                padding-bottom: 10px;
            }
            
            .test-header h3 {
                margin: 0 0 10px 0;
                font-size: 14px;
            }
            
            .btn-test {
                background: #0066cc;
                color: white;
                border: none;
                padding: 4px 8px;
                margin: 2px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 10px;
            }
            
            .btn-test:hover {
                background: #0052a3;
            }
            
            .test-metrics {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 5px;
                margin-bottom: 10px;
                padding-bottom: 10px;
                border-bottom: 1px solid #333;
            }
            
            .metric {
                display: flex;
                justify-content: space-between;
            }
            
            .metric-label {
                color: #aaa;
            }
            
            .metric-value {
                color: #44ff44;
                font-weight: bold;
            }
            
            .test-results {
                max-height: 200px;
                overflow-y: auto;
            }
            
            .test-result {
                margin: 5px 0;
                padding: 5px;
                border-radius: 3px;
                background: rgba(255, 255, 255, 0.1);
            }
            
            .test-result.pass {
                border-left: 3px solid #44ff44;
            }
            
            .test-result.fail {
                border-left: 3px solid #ff4444;
            }
            
            .test-result.warning {
                border-left: 3px solid #ffaa00;
            }
        `;
        
        document.head.appendChild(styles);
        document.body.appendChild(testUI);
        
        // Show/hide with Ctrl+Shift+P
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                testUI.classList.toggle('active');
            }
        });
    }
    
    setupEventListeners() {
        document.getElementById('run-all-tests').addEventListener('click', () => this.runAllTests());
        document.getElementById('run-basic-test').addEventListener('click', () => this.runBasicTest());
        document.getElementById('clear-results').addEventListener('click', () => this.clearResults());
        document.getElementById('export-results').addEventListener('click', () => this.exportResults());
    }
    
    getDeviceType() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTablet = /(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(navigator.userAgent);
        const cores = navigator.hardwareConcurrency || 'unknown';
        const memory = navigator.deviceMemory || 'unknown';
        
        if (isMobile) return `Mobile (${cores}c/${memory}GB)`;
        if (isTablet) return `Tablet (${cores}c/${memory}GB)`;
        return `Desktop (${cores}c/${memory}GB)`;
    }
    
    async runAllTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.clearResults();
        this.addResult('info', 'Starting comprehensive performance test suite...');
        
        try {
            await this.testFrameRate();
            await this.testMemoryUsage();
            await this.testAnimationPerformance();
            await this.testScrollPerformance();
            await this.testResponsiveness();
            await this.testDeviceSpecificOptimizations();
            
            this.addResult('pass', 'All tests completed successfully!');
            this.generateSummaryReport();
        } catch (error) {
            this.addResult('fail', `Test suite failed: ${error.message}`);
        } finally {
            this.isRunning = false;
        }
    }
    
    async runBasicTest() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.clearResults();
        this.addResult('info', 'Running basic performance test...');
        
        try {
            await this.testFrameRate();
            await this.testMemoryUsage();
            this.addResult('pass', 'Basic test completed!');
        } catch (error) {
            this.addResult('fail', `Basic test failed: ${error.message}`);
        } finally {
            this.isRunning = false;
        }
    }
    
    async testFrameRate() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing frame rate...');
            
            let frameCount = 0;
            let startTime = performance.now();
            let minFPS = Infinity;
            let maxFPS = 0;
            let fpsReadings = [];
            
            const testDuration = 3000; // 3 seconds
            
            const measureFrame = () => {
                const currentTime = performance.now();
                frameCount++;
                
                if (frameCount > 1) {
                    const fps = 1000 / (currentTime - this.lastFrameTime);
                    fpsReadings.push(fps);
                    minFPS = Math.min(minFPS, fps);
                    maxFPS = Math.max(maxFPS, fps);
                    
                    document.getElementById('current-fps').textContent = Math.round(fps);
                }
                
                this.lastFrameTime = currentTime;
                
                if (currentTime - startTime < testDuration) {
                    requestAnimationFrame(measureFrame);
                } else {
                    const avgFPS = fpsReadings.reduce((a, b) => a + b, 0) / fpsReadings.length;
                    
                    if (avgFPS >= 55) {
                        this.addResult('pass', `Frame rate: ${Math.round(avgFPS)} FPS (excellent)`);
                    } else if (avgFPS >= 30) {
                        this.addResult('warning', `Frame rate: ${Math.round(avgFPS)} FPS (acceptable)`);
                    } else {
                        this.addResult('fail', `Frame rate: ${Math.round(avgFPS)} FPS (poor)`);
                    }
                    
                    this.addResult('info', `FPS range: ${Math.round(minFPS)} - ${Math.round(maxFPS)}`);
                    resolve();
                }
            };
            
            requestAnimationFrame(measureFrame);
        });
    }
    
    async testMemoryUsage() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing memory usage...');
            
            if ('memory' in performance) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);
                
                document.getElementById('current-memory').textContent = `${usedMB}MB`;
                
                if (usedMB < 50) {
                    this.addResult('pass', `Memory usage: ${usedMB}MB (excellent)`);
                } else if (usedMB < 100) {
                    this.addResult('warning', `Memory usage: ${usedMB}MB (acceptable)`);
                } else {
                    this.addResult('fail', `Memory usage: ${usedMB}MB (high)`);
                }
                
                this.addResult('info', `Memory limit: ${limitMB}MB`);
            } else {
                this.addResult('warning', 'Memory API not available');
            }
            
            resolve();
        });
    }
    
    async testAnimationPerformance() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing animation performance...');
            
            const animatedElements = document.querySelectorAll('[class*="animate-"], .is-visible');
            const activeAnimations = Array.from(animatedElements).filter(el => {
                const computedStyle = window.getComputedStyle(el);
                return computedStyle.animationName !== 'none' || 
                       computedStyle.transitionProperty !== 'none';
            });
            
            document.getElementById('active-animations').textContent = activeAnimations.length;
            
            if (activeAnimations.length < 10) {
                this.addResult('pass', `Active animations: ${activeAnimations.length} (optimal)`);
            } else if (activeAnimations.length < 20) {
                this.addResult('warning', `Active animations: ${activeAnimations.length} (moderate)`);
            } else {
                this.addResult('fail', `Active animations: ${activeAnimations.length} (too many)`);
            }
            
            // Test for performance mode
            if (document.body.classList.contains('performance-mode')) {
                this.addResult('pass', 'Performance mode is active');
            } else {
                this.addResult('info', 'Standard animation mode');
            }
            
            resolve();
        });
    }
    
    async testScrollPerformance() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing scroll performance...');
            
            let scrollEvents = 0;
            let startTime = performance.now();
            
            const scrollHandler = () => {
                scrollEvents++;
            };
            
            window.addEventListener('scroll', scrollHandler, { passive: true });
            
            // Simulate scroll
            window.scrollTo(0, 100);
            setTimeout(() => {
                window.scrollTo(0, 0);
                
                setTimeout(() => {
                    window.removeEventListener('scroll', scrollHandler);
                    
                    if (scrollEvents > 0) {
                        this.addResult('pass', `Scroll events: ${scrollEvents} (responsive)`);
                    } else {
                        this.addResult('warning', 'No scroll events detected');
                    }
                    
                    resolve();
                }, 100);
            }, 100);
        });
    }
    
    async testResponsiveness() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing UI responsiveness...');
            
            const startTime = performance.now();
            
            // Test button click responsiveness
            const testButton = document.createElement('button');
            testButton.style.position = 'absolute';
            testButton.style.left = '-9999px';
            document.body.appendChild(testButton);
            
            testButton.addEventListener('click', () => {
                const responseTime = performance.now() - startTime;
                
                if (responseTime < 16) {
                    this.addResult('pass', `Click response: ${Math.round(responseTime)}ms (excellent)`);
                } else if (responseTime < 50) {
                    this.addResult('warning', `Click response: ${Math.round(responseTime)}ms (acceptable)`);
                } else {
                    this.addResult('fail', `Click response: ${Math.round(responseTime)}ms (slow)`);
                }
                
                document.body.removeChild(testButton);
                resolve();
            });
            
            testButton.click();
        });
    }
    
    async testDeviceSpecificOptimizations() {
        return new Promise((resolve) => {
            this.addResult('info', 'Testing device-specific optimizations...');
            
            const isMobile = window.innerWidth < 768;
            const isLowEnd = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;
            
            if (isMobile) {
                const mobileOptimizations = document.querySelectorAll('.mobile-fade-in, .mobile-slide-up');
                if (mobileOptimizations.length > 0) {
                    this.addResult('pass', 'Mobile optimizations detected');
                } else {
                    this.addResult('warning', 'No mobile-specific optimizations found');
                }
            }
            
            if (isLowEnd && document.body.classList.contains('performance-mode')) {
                this.addResult('pass', 'Low-end device optimizations active');
            } else if (isLowEnd) {
                this.addResult('warning', 'Low-end device detected but performance mode not active');
            }
            
            // Test for reduced motion support
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                this.addResult('pass', 'Reduced motion preference respected');
            }
            
            resolve();
        });
    }
    
    addResult(type, message) {
        const resultDiv = document.createElement('div');
        resultDiv.className = `test-result ${type}`;
        resultDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        
        document.getElementById('test-results').appendChild(resultDiv);
        document.getElementById('test-results').scrollTop = document.getElementById('test-results').scrollHeight;
        
        this.testResults.push({
            timestamp: new Date().toISOString(),
            type,
            message
        });
    }
    
    clearResults() {
        document.getElementById('test-results').innerHTML = '';
        this.testResults = [];
    }
    
    generateSummaryReport() {
        const passCount = this.testResults.filter(r => r.type === 'pass').length;
        const failCount = this.testResults.filter(r => r.type === 'fail').length;
        const warningCount = this.testResults.filter(r => r.type === 'warning').length;
        
        this.addResult('info', `Summary: ${passCount} passed, ${warningCount} warnings, ${failCount} failed`);
        
        if (failCount === 0 && warningCount === 0) {
            this.addResult('pass', '🎉 Perfect performance score!');
        } else if (failCount === 0) {
            this.addResult('warning', '✅ Good performance with minor issues');
        } else {
            this.addResult('fail', '⚠️ Performance issues detected');
        }
    }
    
    exportResults() {
        const report = {
            timestamp: new Date().toISOString(),
            deviceInfo: {
                userAgent: navigator.userAgent,
                deviceType: this.getDeviceType(),
                screenSize: `${window.screen.width}x${window.screen.height}`,
                viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                pixelRatio: window.devicePixelRatio
            },
            testResults: this.testResults
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-test-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.addResult('pass', 'Test results exported successfully');
    }
}

// Initialize performance test suite
const performanceTestSuite = new PerformanceTestSuite();

// Export for global access
window.performanceTestSuite = performanceTestSuite;
