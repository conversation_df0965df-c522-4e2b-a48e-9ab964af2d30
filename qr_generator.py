#!/usr/bin/env python3
"""
Enhanced QR Code Generator with Performance Optimizations
Generates QR codes for URLs and sends notifications when scanned
Features: Caching, Performance Monitoring, Optimized Data Delivery
"""

import qrcode
import io
import base64
import uuid
import json
import time
import gzip
import hashlib
from datetime import datetime, timedelta
from urllib.parse import quote, unquote
from flask import Flask, request, jsonify, render_template_string, send_file, Response
from flask_cors import CORS
import threading
import os
from PIL import Image, ImageDraw, ImageFont
from functools import wraps, lru_cache
import psutil
import gc

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 year cache for static files
CORS(app)

# Performance monitoring
performance_stats = {
    'requests_count': 0,
    'avg_response_time': 0,
    'cache_hits': 0,
    'cache_misses': 0,
    'memory_usage': 0,
    'cpu_usage': 0
}

# Store active QR codes and their metadata
active_qr_codes = {}
scan_notifications = []

# In-memory cache for QR codes and responses
qr_cache = {}
response_cache = {}

# Performance monitoring decorators
def monitor_performance(f):
    """Decorator to monitor API performance"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        performance_stats['requests_count'] += 1

        try:
            result = f(*args, **kwargs)

            # Update performance stats
            end_time = time.time()
            response_time = end_time - start_time

            # Calculate rolling average
            current_avg = performance_stats['avg_response_time']
            count = performance_stats['requests_count']
            performance_stats['avg_response_time'] = (current_avg * (count - 1) + response_time) / count

            return result
        except Exception as e:
            print(f"Error in {f.__name__}: {e}")
            raise
    return decorated_function

def compress_response(f):
    """Decorator to compress JSON responses"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)

        # Check if client accepts gzip
        if 'gzip' in request.headers.get('Accept-Encoding', ''):
            if isinstance(response, dict):
                json_str = json.dumps(response)
                compressed = gzip.compress(json_str.encode('utf-8'))

                flask_response = Response(compressed)
                flask_response.headers['Content-Encoding'] = 'gzip'
                flask_response.headers['Content-Type'] = 'application/json'
                return flask_response

        return jsonify(response) if isinstance(response, dict) else response
    return decorated_function

def cache_response(timeout=300):
    """Decorator to cache responses for specified timeout (seconds)"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{f.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"

            # Check cache
            if cache_key in response_cache:
                cached_data, timestamp = response_cache[cache_key]
                if time.time() - timestamp < timeout:
                    performance_stats['cache_hits'] += 1
                    return cached_data
                else:
                    # Remove expired cache entry
                    del response_cache[cache_key]

            # Cache miss - execute function
            performance_stats['cache_misses'] += 1
            result = f(*args, **kwargs)

            # Store in cache
            response_cache[cache_key] = (result, time.time())

            return result
        return decorated_function
    return decorator

def update_system_stats():
    """Update system performance statistics"""
    try:
        performance_stats['memory_usage'] = psutil.virtual_memory().percent
        performance_stats['cpu_usage'] = psutil.cpu_percent()
    except:
        pass  # Ignore errors if psutil is not available

class QRCodeGenerator:
    def __init__(self):
        self.qr_codes_dir = "generated_qr_codes"
        if not os.path.exists(self.qr_codes_dir):
            os.makedirs(self.qr_codes_dir)

        # Initialize cache cleanup timer
        self.start_cache_cleanup()

    def start_cache_cleanup(self):
        """Start background task to clean up expired cache entries"""
        def cleanup_cache():
            while True:
                time.sleep(300)  # Run every 5 minutes
                current_time = time.time()

                # Clean up QR cache
                expired_keys = [k for k, (_, timestamp) in qr_cache.items()
                               if current_time - timestamp > 3600]  # 1 hour
                for key in expired_keys:
                    del qr_cache[key]

                # Clean up response cache
                expired_keys = [k for k, (_, timestamp) in response_cache.items()
                               if current_time - timestamp > 300]  # 5 minutes
                for key in expired_keys:
                    del response_cache[key]

                # Force garbage collection
                gc.collect()

        cleanup_thread = threading.Thread(target=cleanup_cache, daemon=True)
        cleanup_thread.start()

    @lru_cache(maxsize=100)
    def generate_qr_code_cached(self, url, custom_id=None):
        """Generate a QR code with caching for identical URLs"""
        return self.generate_qr_code(url, custom_id)

    def generate_qr_code(self, url, custom_id=None):
        """Generate a QR code for the given URL with tracking and optimization"""
        if custom_id is None:
            custom_id = str(uuid.uuid4())

        # Check cache first
        cache_key = f"{url}:{custom_id}"
        if cache_key in qr_cache:
            cached_data, timestamp = qr_cache[cache_key]
            if time.time() - timestamp < 3600:  # 1 hour cache
                return cached_data

        # Create tracking URL that includes our notification endpoint
        encoded_url = quote(url, safe='')
        # Auto-detect server URL (works for local and cloud deployment)
        server_url = os.environ.get('SERVER_URL', 'https://nourddinak.pythonanywhere.com')
        tracking_url = f"{server_url}/track/{custom_id}?redirect={encoded_url}"

        # Generate QR code with optimized settings
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,  # Lower error correction for smaller size
            box_size=8,  # Reduced box size for better performance
            border=2,    # Reduced border
        )
        qr.add_data(tracking_url)
        qr.make(fit=True)

        # Create QR code image with optimized settings
        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Optimize image size
        img = img.resize((200, 200), Image.Resampling.LANCZOS)  # Fixed size for consistency

        # Save the image with optimization
        filename = f"qr_{custom_id}.png"
        filepath = os.path.join(self.qr_codes_dir, filename)
        img.save(filepath, optimize=True, quality=85)

        # Store metadata
        metadata = {
            'id': custom_id,
            'original_url': url,
            'tracking_url': tracking_url,
            'created_at': datetime.now().isoformat(),
            'scan_count': 0,
            'filepath': filepath,
            'filename': filename
        }
        active_qr_codes[custom_id] = metadata

        # Cache the result
        result = (custom_id, tracking_url, filepath)
        qr_cache[cache_key] = (result, time.time())

        return result
    
    def add_logo_to_qr(self, qr_img):
        """Add a small logo or styling to the QR code center (optional)"""
        # For now, just return the original image
        # You can add logo overlay logic here if needed
        return qr_img
    
    def get_qr_code_base64(self, qr_id):
        """Get QR code as base64 string for web display"""
        if qr_id not in active_qr_codes:
            return None
        
        filepath = active_qr_codes[qr_id]['filepath']
        if not os.path.exists(filepath):
            return None
        
        with open(filepath, "rb") as img_file:
            img_data = img_file.read()
            base64_string = base64.b64encode(img_data).decode('utf-8')
            return f"data:image/png;base64,{base64_string}"

# Initialize QR generator
qr_generator = QRCodeGenerator()

@app.route('/')
def index():
    """Main page to generate and display QR codes"""
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>QR Code Generator</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; }
            .container { max-width: 800px; margin: 0 auto; }
            .qr-display { text-align: center; margin: 20px 0; }
            .qr-image { max-width: 300px; border: 2px solid #333; border-radius: 10px; }
            .notification { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #00ff00; }
            input, button { padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #333; }
            input { background: #2a2a2a; color: white; width: 300px; }
            button { background: #0066cc; color: white; cursor: pointer; }
            button:hover { background: #0052a3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>QR Code Generator with Scan Tracking</h1>
            
            <div>
                <input type="text" id="urlInput" placeholder="Enter URL to generate QR code" value="https://example.com">
                <button onclick="generateQR()">Generate QR Code</button>
            </div>
            
            <div id="qrDisplay" class="qr-display"></div>
            
            <div>
                <h3>Scan Notifications:</h3>
                <div id="notifications"></div>
            </div>
        </div>
        
        <script>
            const socket = io();
            
            socket.on('qr_scanned', function(data) {
                console.log('QR Code scanned:', data);
                addNotification(data);
                // This is where you'd trigger your video in the main HTML
                triggerVideoPlay(data);
            });
            
            function generateQR() {
                const url = document.getElementById('urlInput').value;
                if (!url) {
                    alert('Please enter a URL');
                    return;
                }
                
                fetch('/generate', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({url: url})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayQR(data);
                    } else {
                        alert('Error generating QR code');
                    }
                });
            }
            
            function displayQR(data) {
                const display = document.getElementById('qrDisplay');
                display.innerHTML = `
                    <h3>QR Code Generated</h3>
                    <img src="${data.qr_image}" class="qr-image" alt="QR Code">
                    <p>QR ID: ${data.qr_id}</p>
                    <p>Tracking URL: <a href="${data.tracking_url}" target="_blank">${data.tracking_url}</a></p>
                    <p>Original URL: ${data.original_url}</p>
                `;
            }
            
            function addNotification(data) {
                const notifications = document.getElementById('notifications');
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.innerHTML = `
                    <strong>QR Code Scanned!</strong><br>
                    QR ID: ${data.qr_id}<br>
                    Time: ${new Date(data.timestamp).toLocaleString()}<br>
                    User Agent: ${data.user_agent || 'Unknown'}<br>
                    IP: ${data.ip || 'Unknown'}
                `;
                notifications.insertBefore(notification, notifications.firstChild);
            }
            
            function triggerVideoPlay(data) {
                // This function will be called when QR is scanned
                // You can customize this to trigger your specific video
                console.log('Triggering video play for scan:', data);
            }
        </script>
    </body>
    </html>
    ''')

@app.route('/generate', methods=['POST'])
@monitor_performance
@compress_response
def generate_qr():
    """Generate a new QR code with performance monitoring"""
    data = request.get_json()
    url = data.get('url')

    if not url:
        return {'success': False, 'error': 'URL is required'}

    try:
        qr_id, tracking_url, filepath = qr_generator.generate_qr_code(url)
        qr_image_base64 = qr_generator.get_qr_code_base64(qr_id)

        return {
            'success': True,
            'qr_id': qr_id,
            'tracking_url': tracking_url,
            'original_url': url,
            'qr_image': qr_image_base64,
            'cache_info': {
                'cached': False,
                'generated_at': datetime.now().isoformat()
            }
        }
    except Exception as e:
        return {'success': False, 'error': str(e)}

# Performance monitoring endpoints
@app.route('/api/performance')
@monitor_performance
@cache_response(timeout=60)
def get_performance_stats():
    """Get current performance statistics"""
    update_system_stats()

    return {
        'performance': performance_stats,
        'cache_stats': {
            'qr_cache_size': len(qr_cache),
            'response_cache_size': len(response_cache),
            'active_qr_codes': len(active_qr_codes)
        },
        'timestamp': datetime.now().isoformat()
    }

@app.route('/api/health')
def health_check():
    """Simple health check endpoint"""
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0-optimized'
    }

@app.route('/api/cache/clear', methods=['POST'])
def clear_cache():
    """Clear all caches (admin endpoint)"""
    global qr_cache, response_cache

    qr_cache.clear()
    response_cache.clear()
    gc.collect()

    return {
        'success': True,
        'message': 'All caches cleared',
        'timestamp': datetime.now().isoformat()
    }

@app.route('/track/<qr_id>')
def track_scan(qr_id):
    """Handle QR code scan and send notification"""
    if qr_id not in active_qr_codes:
        return "QR Code not found", 404

    # Get scan information
    user_agent = request.headers.get('User-Agent', 'Unknown')
    ip_address = request.remote_addr
    timestamp = datetime.now().isoformat()

    # Update scan count
    active_qr_codes[qr_id]['scan_count'] += 1

    # Create notification data
    notification_data = {
        'qr_id': qr_id,
        'timestamp': timestamp,
        'user_agent': user_agent,
        'ip': ip_address,
        'scan_count': active_qr_codes[qr_id]['scan_count']
    }

    # Store notification
    scan_notifications.append(notification_data)

    # Store notification for polling (PythonAnywhere compatible)
    # No WebSocket needed - using polling instead

    # Redirect to original URL
    encoded_redirect = request.args.get('redirect', active_qr_codes[qr_id]['original_url'])
    redirect_url = unquote(encoded_redirect) if encoded_redirect else active_qr_codes[qr_id]['original_url']
    return f'''
    <html>
    <head>
        <title>Redirecting...</title>
        <meta http-equiv="refresh" content="2;url={redirect_url}">
    </head>
    <body style="background: #1a1a1a; color: white; text-align: center; padding: 50px;">
        <h2>QR Code Scanned Successfully!</h2>
        <p>Redirecting to: {redirect_url}</p>
        <p>If you're not redirected automatically, <a href="{redirect_url}" style="color: #00ff00;">click here</a></p>
    </body>
    </html>
    '''

@app.route('/qr/<qr_id>')
def get_qr_image(qr_id):
    """Serve QR code image"""
    if qr_id not in active_qr_codes:
        return "QR Code not found", 404

    filepath = active_qr_codes[qr_id]['filepath']
    if not os.path.exists(filepath):
        return "QR Code image not found", 404

    return send_file(filepath, mimetype='image/png')

@app.route('/api/notifications')
@monitor_performance
@compress_response
@cache_response(timeout=30)
def get_notifications():
    """Get all scan notifications with caching"""
    return scan_notifications

@app.route('/api/poll/<qr_id>')
@monitor_performance
@compress_response
def poll_notifications(qr_id):
    """Poll for new notifications for a specific QR code (optimized)"""
    current_time = time.time()
    recent_notifications = []

    for notification in scan_notifications:
        if notification['qr_id'] == qr_id:
            # Check if notification is recent (last 30 seconds)
            notification_time = datetime.fromisoformat(notification['timestamp']).timestamp()
            if current_time - notification_time < 30:
                recent_notifications.append(notification)

    return {
        'notifications': recent_notifications,
        'count': len(recent_notifications),
        'qr_id': qr_id,
        'timestamp': datetime.now().isoformat()
    }

@app.route('/api/qr-codes')
@monitor_performance
@compress_response
@cache_response(timeout=60)
def get_qr_codes():
    """Get all active QR codes with caching"""
    return list(active_qr_codes.values())

# Optimized static file serving
@app.route('/qr/<qr_id>')
@monitor_performance
def get_qr_image(qr_id):
    """Serve QR code image with caching headers"""
    if qr_id not in active_qr_codes:
        return "QR Code not found", 404

    filepath = active_qr_codes[qr_id]['filepath']
    if not os.path.exists(filepath):
        return "QR Code image not found", 404

    # Add caching headers
    response = send_file(filepath, mimetype='image/png')
    response.headers['Cache-Control'] = 'public, max-age=3600'  # 1 hour cache
    response.headers['ETag'] = f'"{qr_id}"'

    return response



if __name__ == '__main__':
    print("Starting QR Code Generator Server...")
    print("Access the generator at: https://nourddinak.pythonanywhere.com")
    print("Polling notifications enabled for PythonAnywhere compatibility")
    app.run(debug=True, host='0.0.0.0', port=5000)
